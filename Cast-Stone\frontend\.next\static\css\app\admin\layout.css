/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/admin/AdminSidebar.module.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
.AdminSidebar_sidebar__rfiZq {
  width: 280px;
  background: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: sticky;
  top: 0;
  transition: width 0.3s ease;
  z-index: 100;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.AdminSidebar_sidebar__rfiZq.AdminSidebar_collapsed__7WYrK {
  width: 80px;
}

.AdminSidebar_header__QD7p4 {
  padding: 24px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
  background: var(--bg-primary);
}

.AdminSidebar_logo__kFoPC {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.AdminSidebar_logoIcon__SbH7I {
  width: 32px;
  height: 32px;
  color: var(--admin-accent);
  flex-shrink: 0;
}

.AdminSidebar_logoText__vveLr {
  overflow: hidden;
}

.AdminSidebar_logoTitle__jf_fQ {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  white-space: nowrap;
}

.AdminSidebar_logoSubtitle__JCAPu {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 2px 0 0 0;
  white-space: nowrap;
}

.AdminSidebar_toggleButton__o4SJS {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: var(--text-muted);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.AdminSidebar_toggleButton__o4SJS:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.AdminSidebar_toggleButton__o4SJS svg {
  width: 18px;
  height: 18px;
}

.AdminSidebar_nav__N5Gvg {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.AdminSidebar_menuList__4L4bh {
  list-style: none;
  margin: 0;
  padding: 0;
}

.AdminSidebar_menuItem__QfJCb {
  margin: 0;
}

.AdminSidebar_menuLink__VsOPE {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
  border-radius: 0;
  margin: 0 8px;
  border-radius: 8px;
}

.AdminSidebar_menuLink__VsOPE:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.AdminSidebar_menuLink__VsOPE.AdminSidebar_active__kkBic {
  background: var(--admin-accent);
  color: white;
}

.AdminSidebar_menuLink__VsOPE.AdminSidebar_active__kkBic::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: white;
  border-radius: 0 4px 4px 0;
}

.AdminSidebar_menuIcon__1ygfI {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.AdminSidebar_menuLabel__ujH_8 {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.AdminSidebar_menuBadge__yrC9e {
  background: var(--error-color);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: auto;
}

.AdminSidebar_footer__duAxf {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.AdminSidebar_adminInfo__jlmuU {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--bg-secondary);
  border-radius: 8px;
}

.AdminSidebar_adminAvatar__HnCXF {
  width: 40px;
  height: 40px;
  background: var(--admin-accent);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.AdminSidebar_adminDetails__tvHIU {
  overflow: hidden;
  flex: 1;
}

.AdminSidebar_adminName__ZJQQ2 {
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.9rem;
}

.AdminSidebar_adminRole__1ZcUi {
  color: var(--text-secondary);
  margin: 2px 0 0 0;
  font-size: 0.75rem;
  text-transform: capitalize;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.AdminSidebar_logoutButton__kYGST {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  justify-content: center;
}

.AdminSidebar_logoutButton__kYGST:hover {
  background: var(--error-color);
  border-color: var(--error-color);
  color: white;
}

.AdminSidebar_logoutIcon__VruZB {
  width: 16px;
  height: 16px;
}

/* Collapsed state adjustments */
.AdminSidebar_sidebar__rfiZq.AdminSidebar_collapsed__7WYrK .AdminSidebar_header__QD7p4 {
  padding: 24px 16px;
  justify-content: center;
}

.AdminSidebar_sidebar__rfiZq.AdminSidebar_collapsed__7WYrK .AdminSidebar_logoText__vveLr {
  display: none;
}

.AdminSidebar_sidebar__rfiZq.AdminSidebar_collapsed__7WYrK .AdminSidebar_toggleButton__o4SJS {
  position: absolute;
  right: -12px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.AdminSidebar_sidebar__rfiZq.AdminSidebar_collapsed__7WYrK .AdminSidebar_menuLink__VsOPE {
  padding: 12px 16px;
  justify-content: center;
}

.AdminSidebar_sidebar__rfiZq.AdminSidebar_collapsed__7WYrK .AdminSidebar_menuLabel__ujH_8,
.AdminSidebar_sidebar__rfiZq.AdminSidebar_collapsed__7WYrK .AdminSidebar_menuBadge__yrC9e {
  display: none;
}

.AdminSidebar_sidebar__rfiZq.AdminSidebar_collapsed__7WYrK .AdminSidebar_adminInfo__jlmuU {
  justify-content: center;
  padding: 8px;
}

.AdminSidebar_sidebar__rfiZq.AdminSidebar_collapsed__7WYrK .AdminSidebar_adminDetails__tvHIU {
  display: none;
}

.AdminSidebar_sidebar__rfiZq.AdminSidebar_collapsed__7WYrK .AdminSidebar_logoutButton__kYGST {
  padding: 8px;
  justify-content: center;
}

.AdminSidebar_sidebar__rfiZq.AdminSidebar_collapsed__7WYrK .AdminSidebar_logoutButton__kYGST span {
  display: none;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .AdminSidebar_sidebar__rfiZq {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .AdminSidebar_sidebar__rfiZq.AdminSidebar_open__r1keh {
    transform: translateX(0);
  }
  
  .AdminSidebar_sidebar__rfiZq.AdminSidebar_collapsed__7WYrK {
    width: 280px;
    transform: translateX(-100%);
  }
  
  .AdminSidebar_sidebar__rfiZq.AdminSidebar_collapsed__7WYrK.AdminSidebar_open__r1keh {
    transform: translateX(0);
  }
}

/* Scrollbar styling */
.AdminSidebar_nav__N5Gvg::-webkit-scrollbar {
  width: 4px;
}

.AdminSidebar_nav__N5Gvg::-webkit-scrollbar-track {
  background: transparent;
}

.AdminSidebar_nav__N5Gvg::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 2px;
}

.AdminSidebar_nav__N5Gvg::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/admin/AdminHeader.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
.AdminHeader_header__aAWx7 {
  background: white;
  border-bottom: 1px solid var(--cart-border);
  padding: 0 24px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 50;
}

.AdminHeader_left__8aLgs {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.AdminHeader_menuButton__ujE5p {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: var(--cart-text-muted);
  transition: all 0.2s ease;
  display: none;
}

.AdminHeader_menuButton__ujE5p:hover {
  background: var(--background-light);
  color: var(--cart-text-primary);
}

.AdminHeader_menuButton__ujE5p svg {
  width: 20px;
  height: 20px;
}

.AdminHeader_pageInfo__8DYV2 {
  flex: 1;
}

.AdminHeader_pageTitle__4wMPk {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0 0 4px 0;
}

.AdminHeader_breadcrumb__iFNVq {
  font-size: 0.875rem;
  color: var(--cart-text-secondary);
  margin: 0;
}

.AdminHeader_center__nYbc3 {
  flex: 1;
  display: flex;
  justify-content: center;
  max-width: 400px;
}

.AdminHeader_searchContainer__nLXKx {
  position: relative;
  width: 100%;
  max-width: 300px;
}

.AdminHeader_searchIcon__Ce1RS {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  color: var(--cart-text-muted);
}

.AdminHeader_searchInput__9kASv {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid var(--cart-border);
  border-radius: 8px;
  font-size: 14px;
  background: var(--background-light);
  transition: all 0.2s ease;
}

.AdminHeader_searchInput__9kASv:focus {
  outline: none;
  border-color: var(--primary-color);
  background: white;
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.AdminHeader_right__Nnw8N {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
  justify-content: flex-end;
}

.AdminHeader_notificationContainer__4N3Gf {
  position: relative;
}

.AdminHeader_notificationButton__vzW9S {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: var(--cart-text-muted);
  transition: all 0.2s ease;
  position: relative;
}

.AdminHeader_notificationButton__vzW9S:hover {
  background: var(--background-light);
  color: var(--cart-text-primary);
}

.AdminHeader_notificationButton__vzW9S svg {
  width: 20px;
  height: 20px;
}

.AdminHeader_notificationBadge__x5v9q {
  position: absolute;
  top: 4px;
  right: 4px;
  background: var(--cart-error);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 5px;
  border-radius: 8px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.AdminHeader_notificationDropdown__T0oWm {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid var(--cart-border);
  border-radius: 8px;
  box-shadow: 0 4px 20px var(--cart-shadow);
  width: 320px;
  max-height: 400px;
  overflow: hidden;
  z-index: 1000;
  margin-top: 8px;
}

.AdminHeader_notificationHeader__6nPEG {
  padding: 16px 20px;
  border-bottom: 1px solid var(--cart-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.AdminHeader_notificationHeader__6nPEG h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0;
}

.AdminHeader_notificationCount__BGvj7 {
  background: var(--primary-color);
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
}

.AdminHeader_notificationList__WfGxb {
  max-height: 300px;
  overflow-y: auto;
}

.AdminHeader_noNotifications__GUPhf {
  padding: 40px 20px;
  text-align: center;
  color: var(--cart-text-muted);
  font-size: 14px;
}

.AdminHeader_notificationItem__vaivq {
  padding: 16px 20px;
  border-bottom: 1px solid var(--cart-border);
  display: flex;
  gap: 12px;
  transition: background 0.2s ease;
}

.AdminHeader_notificationItem__vaivq:hover {
  background: var(--background-light);
}

.AdminHeader_notificationItem__vaivq:last-child {
  border-bottom: none;
}

.AdminHeader_notificationDot__r2TJx {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-top: 6px;
  flex-shrink: 0;
}

.AdminHeader_notificationDot__r2TJx.AdminHeader_success__bxcnl {
  background: var(--cart-success);
}

.AdminHeader_notificationDot__r2TJx.AdminHeader_error__OmKjB {
  background: var(--cart-error);
}

.AdminHeader_notificationDot__r2TJx.AdminHeader_warning__7bvif {
  background: var(--cart-warning);
}

.AdminHeader_notificationDot__r2TJx.AdminHeader_info__Izcn2 {
  background: var(--primary-color);
}

.AdminHeader_notificationContent__wChyJ {
  flex: 1;
}

.AdminHeader_notificationContent__wChyJ h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0 0 4px 0;
}

.AdminHeader_notificationContent__wChyJ p {
  font-size: 13px;
  color: var(--cart-text-secondary);
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.AdminHeader_notificationTime__Mp4wy {
  font-size: 12px;
  color: var(--cart-text-muted);
}

.AdminHeader_profileContainer__nf38h {
  position: relative;
}

.AdminHeader_profileButton__7HygD {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
}

.AdminHeader_profileButton__7HygD:hover {
  background: var(--background-light);
}

.AdminHeader_profileAvatar__xWp7_ {
  width: 36px;
  height: 36px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.AdminHeader_profileInfo__dhMxU {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.AdminHeader_profileName__3pecE {
  font-size: 14px;
  font-weight: 600;
  color: var(--cart-text-primary);
  line-height: 1.2;
}

.AdminHeader_profileRole__FLorS {
  font-size: 12px;
  color: var(--cart-text-secondary);
  text-transform: capitalize;
  line-height: 1.2;
}

.AdminHeader_profileChevron__zxBZU {
  width: 16px;
  height: 16px;
  color: var(--cart-text-muted);
  transition: transform 0.2s ease;
}

.AdminHeader_profileButton__7HygD:hover .AdminHeader_profileChevron__zxBZU {
  transform: rotate(180deg);
}

.AdminHeader_profileDropdown__WMeNR {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid var(--cart-border);
  border-radius: 8px;
  box-shadow: 0 4px 20px var(--cart-shadow);
  width: 240px;
  overflow: hidden;
  z-index: 1000;
  margin-top: 8px;
}

.AdminHeader_profileDropdownHeader__UEqeB {
  padding: 16px 20px;
  border-bottom: 1px solid var(--cart-border);
  display: flex;
  gap: 12px;
  align-items: center;
}

.AdminHeader_profileDropdownAvatar__Mjcu2 {
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
}

.AdminHeader_profileDropdownName__fZMMK {
  font-size: 14px;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0 0 2px 0;
}

.AdminHeader_profileDropdownEmail__7vfo5 {
  font-size: 12px;
  color: var(--cart-text-secondary);
  margin: 0;
}

.AdminHeader_profileDropdownMenu__19nHi {
  padding: 8px 0;
}

.AdminHeader_profileMenuItem__4Pa8G {
  width: 100%;
  background: none;
  border: none;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: background 0.2s ease;
  font-size: 14px;
  color: var(--cart-text-secondary);
  text-align: left;
}

.AdminHeader_profileMenuItem__4Pa8G:hover {
  background: var(--background-light);
  color: var(--cart-text-primary);
}

.AdminHeader_profileMenuItem__4Pa8G svg {
  width: 16px;
  height: 16px;
}

.AdminHeader_profileMenuDivider__lno3e {
  border: none;
  border-top: 1px solid var(--cart-border);
  margin: 8px 0;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .AdminHeader_header__aAWx7 {
    padding: 0 16px;
    height: 70px;
  }
  
  .AdminHeader_menuButton__ujE5p {
    display: block;
  }
  
  .AdminHeader_pageTitle__4wMPk {
    font-size: 1.25rem;
  }
  
  .AdminHeader_center__nYbc3 {
    display: none;
  }
  
  .AdminHeader_profileInfo__dhMxU {
    display: none;
  }
  
  .AdminHeader_notificationDropdown__T0oWm,
  .AdminHeader_profileDropdown__WMeNR {
    width: 280px;
  }
}

/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/app/admin/layout.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
.layout_loadingContainer__HRXiz {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  background: var(--bg-secondary);
}

.layout_spinner__cLdzg {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--admin-accent);
  border-radius: 50%;
  animation: layout_spin__75keS 1s linear infinite;
}

.layout_loadingContainer__HRXiz p {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

@keyframes layout_spin__75keS {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.layout_adminLayout__wWd19 {
  display: flex;
  min-height: 100vh;
  background: var(--bg-secondary);
}

.layout_mainContent__o_DPk {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* Prevent flex item from overflowing */
}

.layout_content__l1k3B {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background: var(--bg-secondary);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .layout_content__l1k3B {
    padding: 0;
  }
}

