.container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  margin-bottom: 2rem;
}

.headerLeft h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
}

.subtitle {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Alerts */
.alerts {
  margin-bottom: 1.5rem;
}

.error,
.success {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.error {
  background: var(--error-light);
  color: var(--error-dark);
}

.success {
  background: var(--success-light);
  color: var(--success-dark);
}

/* Settings Container */
.settingsContainer {
  display: flex;
  gap: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  min-height: 600px;
}

/* Tabs */
.tabs {
  width: 250px;
  background: var(--gray-50);
  border-right: 1px solid var(--border-color);
  padding: 1.5rem 0;
  flex-shrink: 0;
}

.tab {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
  font-size: 0.95rem;
  font-weight: 500;
}

.tab:hover {
  background: var(--gray-100);
  color: var(--text-primary);
}

.tab.active {
  background: var(--primary-color);
  color: white;
  position: relative;
}

.tab.active::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--primary-dark);
}

/* Tab Content */
.tabContent {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.section {
  max-width: 800px;
}

.sectionHeader {
  margin-bottom: 2rem;
}

.sectionHeader h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.sectionHeader p {
  margin: 0;
  color: var(--text-secondary);
}

/* Settings Grid */
.settingsGrid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.settingCard {
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
}

.cardHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--gray-25);
  border-bottom: 1px solid var(--border-light);
}

.cardHeader svg {
  color: var(--primary-color);
  flex-shrink: 0;
}

.cardHeader h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.cardHeader p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.cardContent {
  padding: 1.5rem;
}

/* Setting Items */
.settingItem {
  margin-bottom: 1.5rem;
}

.settingItem:last-child {
  margin-bottom: 0;
}

.settingItem label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.settingDescription {
  margin: 0.5rem 0 0 0;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

/* Form Inputs */
.textInput,
.numberInput,
.timeInput,
.selectInput {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.95rem;
  transition: border-color 0.2s ease;
}

.textInput:focus,
.numberInput:focus,
.timeInput:focus,
.selectInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-color-10);
}

.numberInput {
  max-width: 150px;
}

.timeInput {
  max-width: 200px;
}

/* Toggle Switch */
.toggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-weight: 500;
  color: var(--text-primary);
}

.toggle input[type="checkbox"] {
  display: none;
}

.toggleSlider {
  position: relative;
  width: 50px;
  height: 26px;
  background: var(--gray-300);
  border-radius: 26px;
  transition: background-color 0.2s ease;
}

.toggleSlider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 22px;
  height: 22px;
  background: white;
  border-radius: 50%;
  transition: transform 0.2s ease;
}

.toggle input[type="checkbox"]:checked + .toggleSlider {
  background: var(--primary-color);
}

.toggle input[type="checkbox"]:checked + .toggleSlider::before {
  transform: translateX(24px);
}

/* Checkbox */
.checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.checkbox input[type="checkbox"] {
  margin: 0;
}

.checkbox:hover {
  color: var(--text-primary);
}

/* Test Buttons */
.testButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.testButton {
  padding: 0.5rem 1rem;
  background: var(--gray-100);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

.testButton:hover {
  background: var(--primary-color-5);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* Section Actions */
.sectionActions {
  display: flex;
  justify-content: flex-end;
  padding-top: 2rem;
  border-top: 1px solid var(--border-light);
}

.saveButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.saveButton:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.saveButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Coming Soon */
.comingSoon {
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.comingSoon svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.comingSoon h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.comingSoon p {
  margin: 0 0 2rem 0;
}

.integrationsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 400px;
  margin: 0 auto;
}

.integrationItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--gray-25);
  border-radius: 8px;
  border: 1px solid var(--border-light);
}

.integrationIcon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.integrationInfo {
  flex: 1;
}

.integrationInfo h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.95rem;
  color: var(--text-primary);
}

.integrationInfo p {
  margin: 0;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.integrationStatus {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  background: var(--success-light);
  color: var(--success-dark);
  border-radius: 12px;
  font-weight: 500;
}

/* Loading */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .settingsContainer {
    flex-direction: column;
    gap: 0;
  }
  
  .tabs {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding: 1rem;
    background: white;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
  
  .tab {
    white-space: nowrap;
    padding: 0.75rem 1rem;
    min-width: fit-content;
  }
  
  .tab.active::after {
    display: none;
  }
  
  .tabContent {
    padding: 1.5rem;
  }
  
  .testButtons {
    flex-direction: column;
  }
  
  .sectionActions {
    justify-content: stretch;
  }
  
  .saveButton {
    width: 100%;
    justify-content: center;
  }
}
