# Frontend Environment Configuration for Cast-Stone
# Next.js Environment Variables

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NEXT_PUBLIC_BACKEND_URL=http://localhost:5000

# Admin Dashboard Configuration
NEXT_PUBLIC_ADMIN_URL=http://localhost:3000/admin

# Application Configuration
NEXT_PUBLIC_APP_NAME=Cast Stone
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Development Configuration
NODE_ENV=development

# Stripe Configuration (for frontend)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# Image Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=5242880
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Authentication Configuration
NEXT_PUBLIC_JWT_EXPIRY=24h

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_INVENTORY_TRACKING=true

# UI Configuration
NEXT_PUBLIC_ITEMS_PER_PAGE=20
NEXT_PUBLIC_PAGINATION_LIMIT=10

# Development Tools
NEXT_PUBLIC_DEBUG_MODE=true
