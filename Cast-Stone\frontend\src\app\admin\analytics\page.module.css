.container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.headerLeft h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
}

.subtitle {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

.headerRight {
  display: flex;
  gap: 1rem;
}

.refreshButton,
.exportButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refreshButton {
  background: white;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.refreshButton:hover {
  background: var(--gray-50);
  border-color: var(--primary-color);
}

.refreshButton.refreshing svg {
  animation: spin 1s linear infinite;
}

.exportButton {
  background: var(--primary-color);
  color: white;
}

.exportButton:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.retryButton {
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 1rem;
}

/* Date Range Selector */
.dateRangeSelector {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.periodButtons {
  display: flex;
  gap: 0.5rem;
}

.periodButton {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.periodButton:hover {
  border-color: var(--primary-color);
  background: var(--primary-color-5);
}

.periodButton.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.customDateInputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 1rem;
  padding-left: 1rem;
  border-left: 1px solid var(--border-color);
}

.dateInput {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.9rem;
}

.dateInput:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Metrics Grid */
.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metricCard {
  background: white;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.metricCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metricHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.metricIcon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--primary-color-10);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
}

.metricGrowth {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.85rem;
  font-weight: 500;
}

.growthPositive {
  color: var(--success-dark);
}

.growthNegative {
  color: var(--error-dark);
}

.growthNeutral {
  color: var(--text-secondary);
}

.metricValue {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.metricLabel {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Content Grid */
.contentGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.card {
  background: white;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-light);
}

.cardHeader h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.viewAllButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: var(--gray-50);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

.viewAllButton:hover {
  background: var(--primary-color-5);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.cardContent {
  padding: 1.5rem;
}

/* Orders List */
.ordersList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.orderItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--gray-25);
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.orderItem:hover {
  background: var(--gray-50);
}

.orderInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.orderNumber {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 0.95rem;
}

.orderCustomer {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.orderMeta {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  text-align: right;
}

.orderAmount {
  font-weight: 600;
  color: var(--text-primary);
}

.orderDate {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

/* Products List */
.productsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.productItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--gray-25);
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.productItem:hover {
  background: var(--gray-50);
}

.productRank {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.85rem;
  flex-shrink: 0;
}

.productInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.productTitle {
  font-weight: 500;
  color: var(--text-primary);
}

.productSales {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.productRevenue {
  font-weight: 600;
  color: var(--success-dark);
}

/* Alerts */
.alertsGrid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.alertItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--gray-25);
  border-radius: 8px;
}

.alertIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--warning-light);
  color: var(--warning-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.alertIcon.critical {
  background: var(--error-light);
  color: var(--error-dark);
}

.alertInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.alertCount {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.alertLabel {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.alertAction {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-light);
}

.alertActionButton {
  width: 100%;
  padding: 0.75rem;
  background: var(--warning-color);
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.alertActionButton:hover {
  background: var(--warning-dark);
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary);
}

.emptyState svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyState p {
  margin: 0;
}

/* Loading and Error */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--error-dark);
  text-align: center;
}

.error h3 {
  margin: 1rem 0 0.5rem 0;
  color: var(--text-primary);
}

.error p {
  margin: 0 0 1rem 0;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .dateRangeSelector {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .customDateInputs {
    margin-left: 0;
    padding-left: 0;
    border-left: none;
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
  }
  
  .metricsGrid {
    grid-template-columns: 1fr;
  }
  
  .contentGrid {
    grid-template-columns: 1fr;
  }
  
  .orderItem,
  .productItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .orderMeta {
    text-align: left;
  }
}
