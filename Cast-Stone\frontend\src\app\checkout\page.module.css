.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main {
  flex: 1;
  padding-top: 120px;
  padding-bottom: 40px;
  background: var(--background-light);
}

.checkoutContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--cart-text-primary);
  margin: 0 0 12px 0;
}

.subtitle {
  font-size: 1.1rem;
  color: var(--cart-text-secondary);
  margin: 0;
}

.checkoutContent {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 40px;
  align-items: start;
}

.formSection {
  background: var(--cart-bg);
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 20px var(--cart-shadow);
}

.summarySection {
  background: var(--cart-bg);
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 20px var(--cart-shadow);
  position: sticky;
  top: 140px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--cart-border);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading p {
  color: var(--cart-text-secondary);
  font-size: 1.1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile responsive */
@media (max-width: 1024px) {
  .checkoutContent {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .summarySection {
    position: static;
    order: -1;
  }
}

@media (max-width: 768px) {
  .main {
    padding-top: 100px;
    padding-bottom: 20px;
  }
  
  .checkoutContainer {
    padding: 0 16px;
  }
  
  .header {
    margin-bottom: 30px;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .formSection,
  .summarySection {
    padding: 24px;
  }
  
  .checkoutContent {
    gap: 20px;
  }
}
