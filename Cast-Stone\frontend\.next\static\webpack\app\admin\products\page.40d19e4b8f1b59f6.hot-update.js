"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/products/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AdminContext */ \"(app-pages-browser)/./src/contexts/AdminContext.tsx\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./page.module.css */ \"(app-pages-browser)/./src/app/admin/products/page.module.css\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_page_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ProductsManagement() {\n    _s();\n    const { hasPermission, addNotification } = (0,_contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__.useAdmin)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddProductModal, setShowAddProductModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showImportModal, setShowImportModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Mock data - replace with actual API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsManagement.useEffect\": ()=>{\n            const fetchProducts = {\n                \"ProductsManagement.useEffect.fetchProducts\": async ()=>{\n                    try {\n                        // Simulate API call\n                        await new Promise({\n                            \"ProductsManagement.useEffect.fetchProducts\": (resolve)=>setTimeout(resolve, 1000)\n                        }[\"ProductsManagement.useEffect.fetchProducts\"]);\n                        setProducts([\n                            {\n                                id: '1',\n                                name: 'Classic Fireplace Mantel',\n                                category: 'fireplaces',\n                                price: 2500,\n                                stock: 12,\n                                status: 'active',\n                                imageUrl: '/images/fireplace-collection.jpg',\n                                description: 'Handcrafted traditional mantel with intricate detailing',\n                                createdAt: '2024-01-10',\n                                updatedAt: '2024-01-15'\n                            },\n                            {\n                                id: '2',\n                                name: 'Modern Fireplace Surround',\n                                category: 'fireplaces',\n                                price: 3200,\n                                stock: 8,\n                                status: 'active',\n                                imageUrl: '/images/fireplace-collection.jpg',\n                                description: 'Contemporary design with clean lines',\n                                createdAt: '2024-01-12',\n                                updatedAt: '2024-01-14'\n                            },\n                            {\n                                id: '3',\n                                name: 'Garden Fountain',\n                                category: 'garden',\n                                price: 1800,\n                                stock: 5,\n                                status: 'active',\n                                imageUrl: '/images/garden-collection.jpg',\n                                description: 'Three-tier fountain perfect for outdoor spaces',\n                                createdAt: '2024-01-08',\n                                updatedAt: '2024-01-13'\n                            },\n                            {\n                                id: '4',\n                                name: 'Decorative Columns',\n                                category: 'architectural',\n                                price: 1200,\n                                stock: 0,\n                                status: 'inactive',\n                                imageUrl: '/images/architectural-collection.jpg',\n                                description: 'Corinthian style columns for grand entrances',\n                                createdAt: '2024-01-05',\n                                updatedAt: '2024-01-10'\n                            }\n                        ]);\n                    } catch (error) {\n                        console.error('Failed to fetch products:', error);\n                        addNotification({\n                            type: 'error',\n                            title: 'Error',\n                            message: 'Failed to load products'\n                        });\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"ProductsManagement.useEffect.fetchProducts\"];\n            fetchProducts();\n        }\n    }[\"ProductsManagement.useEffect\"], [\n        addNotification\n    ]);\n    const categories = [\n        'all',\n        'fireplaces',\n        'garden',\n        'architectural',\n        'decorative'\n    ];\n    const statuses = [\n        'all',\n        'active',\n        'inactive',\n        'draft'\n    ];\n    const filteredProducts = products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n        const matchesStatus = selectedStatus === 'all' || product.status === selectedStatus;\n        return matchesSearch && matchesCategory && matchesStatus;\n    });\n    const handleSelectProduct = (productId)=>{\n        setSelectedProducts((prev)=>prev.includes(productId) ? prev.filter((id)=>id !== productId) : [\n                ...prev,\n                productId\n            ]);\n    };\n    const handleSelectAll = ()=>{\n        if (selectedProducts.length === filteredProducts.length) {\n            setSelectedProducts([]);\n        } else {\n            setSelectedProducts(filteredProducts.map((p)=>p.id));\n        }\n    };\n    const handleDeleteProduct = async (productId)=>{\n        if (!hasPermission('products', 'delete')) {\n            addNotification({\n                type: 'error',\n                title: 'Permission Denied',\n                message: 'You do not have permission to delete products'\n            });\n            return;\n        }\n        if (confirm('Are you sure you want to delete this product?')) {\n            try {\n                // Simulate API call\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                setProducts((prev)=>prev.filter((p)=>p.id !== productId));\n                addNotification({\n                    type: 'success',\n                    title: 'Product Deleted',\n                    message: 'Product has been successfully deleted'\n                });\n            } catch (error) {\n                addNotification({\n                    type: 'error',\n                    title: 'Error',\n                    message: 'Failed to delete product'\n                });\n            }\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        if (!hasPermission('products', 'delete')) {\n            addNotification({\n                type: 'error',\n                title: 'Permission Denied',\n                message: 'You do not have permission to delete products'\n            });\n            return;\n        }\n        if (selectedProducts.length === 0) return;\n        if (confirm(\"Are you sure you want to delete \".concat(selectedProducts.length, \" products?\"))) {\n            try {\n                // Simulate API call\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                setProducts((prev)=>prev.filter((p)=>!selectedProducts.includes(p.id)));\n                setSelectedProducts([]);\n                addNotification({\n                    type: 'success',\n                    title: 'Products Deleted',\n                    message: \"\".concat(selectedProducts.length, \" products have been deleted\")\n                });\n            } catch (error) {\n                addNotification({\n                    type: 'error',\n                    title: 'Error',\n                    message: 'Failed to delete products'\n                });\n            }\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().loading),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().spinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading products...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n            lineNumber: 210,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productsPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().title),\n                                children: \"Products Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().subtitle),\n                                children: \"Manage your product catalog, inventory, and pricing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerActions),\n                        children: [\n                            hasPermission('products', 'create') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().primaryButton),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Product\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryButton),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryButton),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().toolbar),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchContainer),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchIcon)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search products...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchInput)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filters),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterButton), \" \").concat(showFilters ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                onClick: ()=>setShowFilters(!showFilters),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Filters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterDropdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterGroup),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedCategory,\n                                                onChange: (e)=>setSelectedCategory(e.target.value),\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: category,\n                                                        children: category === 'all' ? 'All Categories' : category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterGroup),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedStatus,\n                                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                                children: statuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: status,\n                                                        children: status === 'all' ? 'All Statuses' : status\n                                                    }, status, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    selectedProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().bulkActions),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().selectedCount),\n                                children: [\n                                    selectedProducts.length,\n                                    \" selected\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().bulkDeleteButton),\n                                onClick: handleBulkDelete,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Delete Selected\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().tableContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().table),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedProducts.length === filteredProducts.length && filteredProducts.length > 0,\n                                                onChange: handleSelectAll\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Product\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Price\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Stock\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Updated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: selectedProducts.includes(product.id),\n                                                    onChange: ()=>handleSelectProduct(product.id)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productCell),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productImage),\n                                                            children: product.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.imageUrl,\n                                                                alt: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().imagePlaceholder),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productInfo),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productName),\n                                                                    children: product.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productDescription),\n                                                                    children: product.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().category),\n                                                    children: product.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().price),\n                                                    children: [\n                                                        \"$\",\n                                                        product.price.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().stock), \" \").concat(product.stock === 0 ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().outOfStock) : ''),\n                                                    children: product.stock\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().status), \" \").concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default())[product.status]),\n                                                    children: product.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().date),\n                                                    children: new Date(product.updatedAt).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actions),\n                                                    children: [\n                                                        hasPermission('products', 'read') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"View\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        hasPermission('products', 'update') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"Edit\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        hasPermission('products', 'delete') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"Delete\",\n                                                            onClick: ()=>handleDeleteProduct(product.id),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"More\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, product.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().emptyState),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().emptyIcon),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"No products found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Try adjusting your search or filter criteria\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n        lineNumber: 218,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsManagement, \"Sd8k+dtmGd1AIybCnWSGiosquAY=\", false, function() {\n    return [\n        _contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__.useAdmin\n    ];\n});\n_c = ProductsManagement;\nvar _c;\n$RefreshReg$(_c, \"ProductsManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/page.tsx\n"));

/***/ })

});