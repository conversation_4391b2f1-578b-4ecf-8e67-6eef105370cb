.checkoutForm {
  width: 100%;
}

.stepIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
  position: relative;
}

.stepWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  max-width: 200px;
}

.step {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid var(--cart-border);
  background: var(--cart-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: 12px;
}

.step:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.step.active {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

.step.completed {
  border-color: var(--cart-success);
  background: var(--cart-success);
  color: white;
}

.stepIcon {
  width: 24px;
  height: 24px;
}

.stepNumber {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  background: var(--cart-text-primary);
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step.active .stepNumber {
  background: white;
  color: var(--primary-color);
}

.step.completed .stepNumber {
  background: white;
  color: var(--cart-success);
}

.stepTitle {
  font-size: 14px;
  font-weight: 500;
  color: var(--cart-text-secondary);
  text-align: center;
  max-width: 120px;
}

.stepConnector {
  position: absolute;
  top: 30px;
  left: 50%;
  right: -50%;
  height: 3px;
  background: var(--cart-border);
  z-index: -1;
}

.stepConnector.completed {
  background: var(--cart-success);
}

.stepContent {
  background: var(--background-light);
  border-radius: 12px;
  padding: 32px;
}

.form {
  width: 100%;
}

.sectionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0 0 24px 0;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.formRow:has(.formGroup:nth-child(3)) {
  grid-template-columns: 1fr 1fr 1fr;
}

.formGroup {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.label {
  font-size: 14px;
  font-weight: 500;
  color: var(--cart-text-primary);
  margin-bottom: 8px;
}

.input {
  padding: 12px 16px;
  border: 2px solid var(--cart-border);
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
  background: var(--cart-bg);
  color: var(--cart-text-primary);
}

.input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.input.error {
  border-color: var(--cart-error);
}

.errorMessage {
  color: var(--cart-error);
  font-size: 12px;
  margin-top: 4px;
}

.continueButton {
  background: var(--primary-color);
  color: white;
  padding: 16px 32px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  margin-top: 20px;
}

.continueButton:hover:not(:disabled) {
  background: var(--cart-primary-hover);
  transform: translateY(-1px);
}

.continueButton:disabled {
  background: var(--cart-text-muted);
  cursor: not-allowed;
  transform: none;
}

.buttonGroup {
  display: flex;
  gap: 16px;
  margin-top: 24px;
}

.backButton {
  background: transparent;
  color: var(--cart-text-secondary);
  padding: 16px 32px;
  border: 2px solid var(--cart-border);
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
}

.backButton:hover {
  background: var(--cart-border);
  color: var(--cart-text-primary);
}

.placeOrderButton {
  background: var(--cart-success);
  color: white;
  padding: 16px 32px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 2;
}

.placeOrderButton:hover {
  background: #047857;
  transform: translateY(-1px);
}

.paymentSection,
.reviewSection {
  text-align: center;
  padding: 40px 20px;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .stepIndicator {
    margin-bottom: 30px;
  }
  
  .step {
    width: 50px;
    height: 50px;
  }
  
  .stepIcon {
    width: 20px;
    height: 20px;
  }
  
  .stepNumber {
    width: 18px;
    height: 18px;
    font-size: 11px;
  }
  
  .stepTitle {
    font-size: 12px;
    max-width: 100px;
  }
  
  .stepContent {
    padding: 24px;
  }
  
  .formRow {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .formRow:has(.formGroup:nth-child(3)) {
    grid-template-columns: 1fr;
  }
  
  .buttonGroup {
    flex-direction: column;
  }
  
  .backButton,
  .continueButton,
  .placeOrderButton {
    width: 100%;
  }
}
