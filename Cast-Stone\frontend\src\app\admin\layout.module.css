.loadingContainer {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  background: var(--bg-secondary);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--admin-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loadingContainer p {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.adminLayout {
  display: flex;
  min-height: 100vh;
  background: var(--bg-secondary);
}

.mainContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* Prevent flex item from overflowing */
}

.content {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background: var(--bg-secondary);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .content {
    padding: 0;
  }
}
