.container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.headerLeft h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
}

.subtitle {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

.headerRight {
  display: flex;
  gap: 1rem;
}

.createButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.createButton:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* Table */
.tableContainer {
  background: white;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  margin-bottom: 2rem;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background: var(--gray-50);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  font-size: 0.9rem;
}

.table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-light);
  vertical-align: top;
}

.tableRow:hover {
  background: var(--gray-25);
}

/* Admin Info */
.adminInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.adminAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-color-10);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  flex-shrink: 0;
}

.adminDetails {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.adminName {
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.youBadge {
  font-size: 0.75rem;
  padding: 0.125rem 0.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 12px;
  font-weight: 600;
}

.adminEmail {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.date {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.createdInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.createdBy {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-style: italic;
}

/* Role Badges */
.roleBadge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.roleSuperAdmin {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #8b5a00;
}

.roleAdmin {
  background: var(--primary-light);
  color: var(--primary-dark);
}

.roleManager {
  background: var(--info-light);
  color: var(--info-dark);
}

.roleViewer {
  background: var(--gray-100);
  color: var(--gray-700);
}

.roleDefault {
  background: var(--gray-100);
  color: var(--gray-700);
}

/* Status */
.statusContainer {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.statusBadge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  width: fit-content;
}

.statusActive {
  background: var(--success-light);
  color: var(--success-dark);
}

.statusInactive {
  background: var(--error-light);
  color: var(--error-dark);
}

.passwordWarning {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: var(--warning-light);
  color: var(--warning-dark);
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  width: fit-content;
}

/* Actions */
.actions {
  display: flex;
  gap: 0.5rem;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

.actionButton:hover {
  transform: translateY(-1px);
}

.activateButton:hover {
  border-color: var(--success-color);
  color: var(--success-color);
  background: var(--success-light);
}

.deactivateButton:hover {
  border-color: var(--warning-color);
  color: var(--warning-color);
  background: var(--warning-light);
}

.deleteButton:hover {
  border-color: var(--error-color);
  color: var(--error-color);
  background: var(--error-light);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modalContent {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modalHeader h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.closeButton:hover {
  background: var(--gray-100);
}

.modalBody {
  padding: 1.5rem;
}

/* Form */
.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.formInput,
.formSelect {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.formInput:focus,
.formSelect:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-color-10);
}

/* Permissions */
.permissionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 0.5rem;
}

.permissionCategory {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
}

.permissionCategory h4 {
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-light);
  padding-bottom: 0.5rem;
}

.permissionActions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.permissionCheckbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.permissionCheckbox input[type="checkbox"] {
  margin: 0;
}

.permissionCheckbox:hover {
  color: var(--text-primary);
}

/* Modal Actions */
.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-light);
}

.cancelButton,
.submitButton {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton {
  background: white;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.cancelButton:hover {
  background: var(--gray-50);
  border-color: var(--text-secondary);
}

.submitButton {
  background: var(--primary-color);
  color: white;
  border: none;
}

.submitButton:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.emptyState svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyState h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.emptyState p {
  margin: 0;
}

/* Loading and Error */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: var(--error-light);
  color: var(--error-dark);
  border-radius: 8px;
  margin-bottom: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .table {
    font-size: 0.9rem;
  }
  
  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
  }
  
  .adminInfo {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .permissionsGrid {
    grid-template-columns: 1fr;
  }
  
  .modalActions {
    flex-direction: column;
  }
}
