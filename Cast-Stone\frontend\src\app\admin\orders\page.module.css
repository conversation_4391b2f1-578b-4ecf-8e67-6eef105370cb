.container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.headerLeft h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
}

.subtitle {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

.headerRight {
  display: flex;
  gap: 1rem;
}

.exportButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.exportButton:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* Filters */
.filtersSection {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: center;
}

.searchBar {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.searchIcon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: border-color 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-color-10);
}

.filterToggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filterToggle:hover,
.filterToggle.active {
  border-color: var(--primary-color);
  background: var(--primary-color-5);
}

.filtersPanel {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.filterRow {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.filterSelect,
.filterInput {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.9rem;
  min-width: 150px;
}

.filterSelect:focus,
.filterInput:focus {
  outline: none;
  border-color: var(--primary-color);
}

.clearFilters {
  padding: 0.5rem 1rem;
  background: var(--gray-100);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.clearFilters:hover {
  background: var(--gray-200);
}

/* Table */
.tableContainer {
  background: white;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  margin-bottom: 2rem;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background: var(--gray-50);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  font-size: 0.9rem;
}

.table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-light);
  vertical-align: top;
}

.tableRow:hover {
  background: var(--gray-25);
}

.orderInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.orderNumber {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 0.95rem;
}

.itemCount {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.customerInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.customerName {
  font-weight: 500;
  color: var(--text-primary);
}

.customerEmail {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.date {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.total {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
}

/* Status Badges */
.statusBadge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Financial Status */
.statusPending { background: var(--warning-light); color: var(--warning-dark); }
.statusAuthorized { background: var(--info-light); color: var(--info-dark); }
.statusPaid { background: var(--success-light); color: var(--success-dark); }
.statusPartiallyPaid { background: var(--warning-light); color: var(--warning-dark); }
.statusRefunded { background: var(--gray-100); color: var(--gray-700); }
.statusPartiallyRefunded { background: var(--gray-100); color: var(--gray-700); }
.statusVoided { background: var(--error-light); color: var(--error-dark); }

/* Fulfillment Status */
.statusUnfulfilled { background: var(--warning-light); color: var(--warning-dark); }
.statusPartial { background: var(--info-light); color: var(--info-dark); }
.statusFulfilled { background: var(--success-light); color: var(--success-dark); }
.statusRestocked { background: var(--gray-100); color: var(--gray-700); }

/* Order Status */
.statusOpen { background: var(--success-light); color: var(--success-dark); }
.statusClosed { background: var(--gray-100); color: var(--gray-700); }
.statusCancelled { background: var(--error-light); color: var(--error-dark); }

.statusDefault { background: var(--gray-100); color: var(--gray-700); }

/* Actions */
.actions {
  display: flex;
  gap: 0.5rem;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

.actionButton:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: var(--primary-color-5);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
}

.paginationButton {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.paginationButton:hover:not(:disabled) {
  border-color: var(--primary-color);
  background: var(--primary-color-5);
}

.paginationButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.paginationInfo {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modalContent {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modalHeader h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.closeButton:hover {
  background: var(--gray-100);
}

.modalBody {
  padding: 1.5rem;
}

.orderDetails {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.orderSection h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-light);
  padding-bottom: 0.5rem;
}

.orderSection p {
  margin: 0.5rem 0;
  color: var(--text-secondary);
}

.orderItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--gray-25);
  border-radius: 6px;
  margin-bottom: 0.5rem;
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.emptyState svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyState h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.emptyState p {
  margin: 0;
}

/* Loading and Error */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: var(--error-light);
  color: var(--error-dark);
  border-radius: 8px;
  margin-bottom: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .filtersSection {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filterRow {
    flex-direction: column;
    align-items: stretch;
  }
  
  .table {
    font-size: 0.9rem;
  }
  
  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
  }
}
