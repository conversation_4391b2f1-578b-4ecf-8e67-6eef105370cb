/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/app/admin/settings/page.module.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
.page_container__c_myD {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.page_header__dAxZp {
  margin-bottom: 2rem;
}

.page_headerLeft__ujMZV h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
}

.page_subtitle__SagGD {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Alerts */
.page_alerts__R7Jlf {
  margin-bottom: 1.5rem;
}

.page_error__6BizG,
.page_success__SNS6m {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.page_error__6BizG {
  background: var(--error-light);
  color: var(--error-dark);
}

.page_success__SNS6m {
  background: var(--success-light);
  color: var(--success-dark);
}

/* Settings Container */
.page_settingsContainer__Okpa1 {
  display: flex;
  gap: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  min-height: 600px;
}

/* Tabs */
.page_tabs__JgIBi {
  width: 250px;
  background: var(--gray-50);
  border-right: 1px solid var(--border-color);
  padding: 1.5rem 0;
  flex-shrink: 0;
}

.page_tab__W3aCs {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
  font-size: 0.95rem;
  font-weight: 500;
}

.page_tab__W3aCs:hover {
  background: var(--gray-100);
  color: var(--text-primary);
}

.page_tab__W3aCs.page_active__P6kbz {
  background: var(--primary-color);
  color: white;
  position: relative;
}

.page_tab__W3aCs.page_active__P6kbz::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--primary-dark);
}

/* Tab Content */
.page_tabContent__uHsAV {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.page_section__HqLEy {
  max-width: 800px;
}

.page_sectionHeader___tqm3 {
  margin-bottom: 2rem;
}

.page_sectionHeader___tqm3 h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.page_sectionHeader___tqm3 p {
  margin: 0;
  color: var(--text-secondary);
}

/* Settings Grid */
.page_settingsGrid__sMJWK {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.page_settingCard__vUcjH {
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
}

.page_cardHeader__mK6lD {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--gray-25);
  border-bottom: 1px solid var(--border-light);
}

.page_cardHeader__mK6lD svg {
  color: var(--primary-color);
  flex-shrink: 0;
}

.page_cardHeader__mK6lD h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.page_cardHeader__mK6lD p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.page_cardContent__IJGcF {
  padding: 1.5rem;
}

/* Setting Items */
.page_settingItem__3DVeV {
  margin-bottom: 1.5rem;
}

.page_settingItem__3DVeV:last-child {
  margin-bottom: 0;
}

.page_settingItem__3DVeV label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.page_settingDescription__plFRh {
  margin: 0.5rem 0 0 0;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

/* Form Inputs */
.page_textInput__BXsWv,
.page_numberInput__3celL,
.page_timeInput__RQBVN,
.page_selectInput__4Dg2f {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.95rem;
  transition: border-color 0.2s ease;
}

.page_textInput__BXsWv:focus,
.page_numberInput__3celL:focus,
.page_timeInput__RQBVN:focus,
.page_selectInput__4Dg2f:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-color-10);
}

.page_numberInput__3celL {
  max-width: 150px;
}

.page_timeInput__RQBVN {
  max-width: 200px;
}

/* Toggle Switch */
.page_toggle__W64_U {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-weight: 500;
  color: var(--text-primary);
}

.page_toggle__W64_U input[type="checkbox"] {
  display: none;
}

.page_toggleSlider__Xg2_z {
  position: relative;
  width: 50px;
  height: 26px;
  background: var(--gray-300);
  border-radius: 26px;
  transition: background-color 0.2s ease;
}

.page_toggleSlider__Xg2_z::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 22px;
  height: 22px;
  background: white;
  border-radius: 50%;
  transition: transform 0.2s ease;
}

.page_toggle__W64_U input[type="checkbox"]:checked + .page_toggleSlider__Xg2_z {
  background: var(--primary-color);
}

.page_toggle__W64_U input[type="checkbox"]:checked + .page_toggleSlider__Xg2_z::before {
  transform: translateX(24px);
}

/* Checkbox */
.page_checkbox__mcI_A {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.page_checkbox__mcI_A input[type="checkbox"] {
  margin: 0;
}

.page_checkbox__mcI_A:hover {
  color: var(--text-primary);
}

/* Test Buttons */
.page_testButtons__bJoMm {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.page_testButton__y7y0T {
  padding: 0.5rem 1rem;
  background: var(--gray-100);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

.page_testButton__y7y0T:hover {
  background: var(--primary-color-5);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* Section Actions */
.page_sectionActions__43bQQ {
  display: flex;
  justify-content: flex-end;
  padding-top: 2rem;
  border-top: 1px solid var(--border-light);
}

.page_saveButton__jmOHo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page_saveButton__jmOHo:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.page_saveButton__jmOHo:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Coming Soon */
.page_comingSoon__JHEYb {
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.page_comingSoon__JHEYb svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.page_comingSoon__JHEYb h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.page_comingSoon__JHEYb p {
  margin: 0 0 2rem 0;
}

.page_integrationsList__rDh7Q {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 400px;
  margin: 0 auto;
}

.page_integrationItem__mLEXf {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--gray-25);
  border-radius: 8px;
  border: 1px solid var(--border-light);
}

.page_integrationIcon__pfIef {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.page_integrationInfo__mAcvf {
  flex: 1;
}

.page_integrationInfo__mAcvf h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.95rem;
  color: var(--text-primary);
}

.page_integrationInfo__mAcvf p {
  margin: 0;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.page_integrationStatus__cuLHf {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  background: var(--success-light);
  color: var(--success-dark);
  border-radius: 12px;
  font-weight: 500;
}

/* Loading */
.page_loading__tFJuW {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.page_spinner__ve6Uu {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: page_spin__0Lkc9 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes page_spin__0Lkc9 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .page_container__c_myD {
    padding: 1rem;
  }
  
  .page_settingsContainer__Okpa1 {
    flex-direction: column;
    gap: 0;
  }
  
  .page_tabs__JgIBi {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding: 1rem;
    background: white;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
  
  .page_tab__W3aCs {
    white-space: nowrap;
    padding: 0.75rem 1rem;
    min-width: -moz-fit-content;
    min-width: fit-content;
  }
  
  .page_tab__W3aCs.page_active__P6kbz::after {
    display: none;
  }
  
  .page_tabContent__uHsAV {
    padding: 1.5rem;
  }
  
  .page_testButtons__bJoMm {
    flex-direction: column;
  }
  
  .page_sectionActions__43bQQ {
    justify-content: stretch;
  }
  
  .page_saveButton__jmOHo {
    width: 100%;
    justify-content: center;
  }
}

