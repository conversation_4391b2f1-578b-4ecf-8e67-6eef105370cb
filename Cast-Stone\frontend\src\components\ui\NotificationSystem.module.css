.notificationContainer {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
  width: 100%;
}

.notification {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 20px var(--cart-shadow);
  background: var(--cart-bg);
  border-left: 4px solid;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.notification.visible {
  transform: translateX(0);
  opacity: 1;
}

.notification.removing {
  transform: translateX(100%);
  opacity: 0;
}

.notification.success {
  border-left-color: var(--cart-success);
}

.notification.error {
  border-left-color: var(--cart-error);
}

.notification.warning {
  border-left-color: var(--cart-warning);
}

.notification.info {
  border-left-color: var(--primary-color);
}

.iconWrapper {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  width: 20px;
  height: 20px;
}

.success .icon {
  color: var(--cart-success);
}

.error .icon {
  color: var(--cart-error);
}

.warning .icon {
  color: var(--cart-warning);
}

.info .icon {
  color: var(--primary-color);
}

.content {
  flex: 1;
  min-width: 0;
}

.title {
  font-size: 14px;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.message {
  font-size: 13px;
  color: var(--cart-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: var(--cart-text-muted);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.closeButton:hover {
  background: var(--cart-border);
  color: var(--cart-text-primary);
}

.closeButton svg {
  width: 16px;
  height: 16px;
}

/* Progress bar for timed notifications */
.notification::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: currentColor;
  opacity: 0.3;
  animation: progress 5s linear forwards;
}

.notification.success::after {
  background: var(--cart-success);
}

.notification.error::after {
  background: var(--cart-error);
}

.notification.warning::after {
  background: var(--cart-warning);
}

.notification.info::after {
  background: var(--primary-color);
}

@keyframes progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .notificationContainer {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .notification {
    padding: 14px;
    gap: 10px;
  }
  
  .title {
    font-size: 13px;
  }
  
  .message {
    font-size: 12px;
  }
  
  .iconWrapper {
    width: 20px;
    height: 20px;
  }
  
  .icon {
    width: 18px;
    height: 18px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .notification {
    border-width: 2px;
    border-style: solid;
  }
  
  .notification.success {
    border-color: var(--cart-success);
  }
  
  .notification.error {
    border-color: var(--cart-error);
  }
  
  .notification.warning {
    border-color: var(--cart-warning);
  }
  
  .notification.info {
    border-color: var(--primary-color);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .notification {
    transition: opacity 0.2s ease;
    transform: none;
  }
  
  .notification.visible {
    transform: none;
  }
  
  .notification.removing {
    transform: none;
  }
  
  .notification::after {
    animation: none;
  }
}
