"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main-app",{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/client/app-find-source-map-url.js ***!
  \******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"findSourceMapURL\", ({\n    enumerable: true,\n    get: function() {\n        return findSourceMapURL;\n    }\n}));\nconst basePath =  false || '';\nconst pathname = \"\" + basePath + \"/__nextjs_source-map\";\nconst findSourceMapURL =  true ? function findSourceMapURL(filename) {\n    if (filename === '') {\n        return null;\n    }\n    if (filename.startsWith(document.location.origin) && filename.includes('/_next/static')) {\n        // This is a request for a client chunk. This can only happen when\n        // using Turbopack. In this case, since we control how those source\n        // maps are generated, we can safely assume that the sourceMappingURL\n        // is relative to the filename, with an added `.map` extension. The\n        // browser can just request this file, and it gets served through the\n        // normal dev server, without the need to route this through\n        // the `/__nextjs_source-map` dev middleware.\n        return \"\" + filename + \".map\";\n    }\n    const url = new URL(pathname, document.location.origin);\n    url.searchParams.set('filename', filename);\n    return url.href;\n} : 0;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-find-source-map-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-index.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/client/app-index.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// imports polyfill from `@next/polyfill-module` after build.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hydrate\", ({\n    enumerable: true,\n    get: function() {\n        return hydrate;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n__webpack_require__(/*! ../build/polyfills/polyfill-module */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/polyfill-module.js\");\n__webpack_require__(/*! ./components/globals/patch-console */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/patch-console.js\");\n__webpack_require__(/*! ./components/globals/handle-global-errors */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/handle-global-errors.js\");\nconst _client = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/client.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _client1 = __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _onrecoverableerror = __webpack_require__(/*! ./react-client-callbacks/on-recoverable-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\");\nconst _errorboundarycallbacks = __webpack_require__(/*! ./react-client-callbacks/error-boundary-callbacks */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js\");\nconst _appcallserver = __webpack_require__(/*! ./app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ./app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ./components/app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _approuter = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./components/app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\"));\nconst _createinitialrouterstate = __webpack_require__(/*! ./components/router-reducer/create-initial-router-state */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _appbuildid = __webpack_require__(/*! ./app-build-id */ \"(app-pages-browser)/./node_modules/next/dist/client/app-build-id.js\");\n/// <reference types=\"react-dom/experimental\" />\nconst appElement = document;\nconst encoder = new TextEncoder();\nlet initialServerDataBuffer = undefined;\nlet initialServerDataWriter = undefined;\nlet initialServerDataLoaded = false;\nlet initialServerDataFlushed = false;\nlet initialFormStateData = null;\nfunction nextServerDataCallback(seg) {\n    if (seg[0] === 0) {\n        initialServerDataBuffer = [];\n    } else if (seg[0] === 1) {\n        if (!initialServerDataBuffer) throw Object.defineProperty(new Error('Unexpected server data: missing bootstrap script.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E18\",\n            enumerable: false,\n            configurable: true\n        });\n        if (initialServerDataWriter) {\n            initialServerDataWriter.enqueue(encoder.encode(seg[1]));\n        } else {\n            initialServerDataBuffer.push(seg[1]);\n        }\n    } else if (seg[0] === 2) {\n        initialFormStateData = seg[1];\n    } else if (seg[0] === 3) {\n        if (!initialServerDataBuffer) throw Object.defineProperty(new Error('Unexpected server data: missing bootstrap script.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E18\",\n            enumerable: false,\n            configurable: true\n        });\n        // Decode the base64 string back to binary data.\n        const binaryString = atob(seg[1]);\n        const decodedChunk = new Uint8Array(binaryString.length);\n        for(var i = 0; i < binaryString.length; i++){\n            decodedChunk[i] = binaryString.charCodeAt(i);\n        }\n        if (initialServerDataWriter) {\n            initialServerDataWriter.enqueue(decodedChunk);\n        } else {\n            initialServerDataBuffer.push(decodedChunk);\n        }\n    }\n}\nfunction isStreamErrorOrUnfinished(ctr) {\n    // If `desiredSize` is null, it means the stream is closed or errored. If it is lower than 0, the stream is still unfinished.\n    return ctr.desiredSize === null || ctr.desiredSize < 0;\n}\n// There might be race conditions between `nextServerDataRegisterWriter` and\n// `DOMContentLoaded`. The former will be called when React starts to hydrate\n// the root, the latter will be called when the DOM is fully loaded.\n// For streaming, the former is called first due to partial hydration.\n// For non-streaming, the latter can be called first.\n// Hence, we use two variables `initialServerDataLoaded` and\n// `initialServerDataFlushed` to make sure the writer will be closed and\n// `initialServerDataBuffer` will be cleared in the right time.\nfunction nextServerDataRegisterWriter(ctr) {\n    if (initialServerDataBuffer) {\n        initialServerDataBuffer.forEach((val)=>{\n            ctr.enqueue(typeof val === 'string' ? encoder.encode(val) : val);\n        });\n        if (initialServerDataLoaded && !initialServerDataFlushed) {\n            if (isStreamErrorOrUnfinished(ctr)) {\n                ctr.error(Object.defineProperty(new Error('The connection to the page was unexpectedly closed, possibly due to the stop button being clicked, loss of Wi-Fi, or an unstable internet connection.'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E117\",\n                    enumerable: false,\n                    configurable: true\n                }));\n            } else {\n                ctr.close();\n            }\n            initialServerDataFlushed = true;\n            initialServerDataBuffer = undefined;\n        }\n    }\n    initialServerDataWriter = ctr;\n}\n// When `DOMContentLoaded`, we can close all pending writers to finish hydration.\nconst DOMContentLoaded = function() {\n    if (initialServerDataWriter && !initialServerDataFlushed) {\n        initialServerDataWriter.close();\n        initialServerDataFlushed = true;\n        initialServerDataBuffer = undefined;\n    }\n    initialServerDataLoaded = true;\n};\n_c = DOMContentLoaded;\n// It's possible that the DOM is already loaded.\nif (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', DOMContentLoaded, false);\n} else {\n    // Delayed in marco task to ensure it's executed later than hydration\n    setTimeout(DOMContentLoaded);\n}\nconst nextServerDataLoadingGlobal = self.__next_f = self.__next_f || [];\nnextServerDataLoadingGlobal.forEach(nextServerDataCallback);\nnextServerDataLoadingGlobal.push = nextServerDataCallback;\nconst readable = new ReadableStream({\n    start (controller) {\n        nextServerDataRegisterWriter(controller);\n    }\n});\nconst initialServerResponse = (0, _client1.createFromReadableStream)(readable, {\n    callServer: _appcallserver.callServer,\n    findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n});\nfunction ServerRoot(param) {\n    let { pendingActionQueue } = param;\n    const initialRSCPayload = (0, _react.use)(initialServerResponse);\n    const actionQueue = (0, _react.use)(pendingActionQueue);\n    const router = /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuter.default, {\n        actionQueue: actionQueue,\n        globalErrorComponentAndStyles: initialRSCPayload.G,\n        assetPrefix: initialRSCPayload.p\n    });\n    if ( true && initialRSCPayload.m) {\n        // We provide missing slot information in a context provider only during development\n        // as we log some additional information about the missing slots in the console.\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.MissingSlotContext, {\n            value: initialRSCPayload.m,\n            children: router\n        });\n    }\n    return router;\n}\n_c1 = ServerRoot;\nconst StrictModeIfEnabled =  true ? _react.default.StrictMode : 0;\nfunction Root(param) {\n    let { children } = param;\n    if (false) {}\n    return children;\n}\n_c2 = Root;\nconst reactRootOptions = {\n    onRecoverableError: _onrecoverableerror.onRecoverableError,\n    onCaughtError: _errorboundarycallbacks.onCaughtError,\n    onUncaughtError: _errorboundarycallbacks.onUncaughtError\n};\nfunction hydrate(instrumentationHooks) {\n    // React overrides `.then` and doesn't return a new promise chain,\n    // so we wrap the action queue in a promise to ensure that its value\n    // is defined when the promise resolves.\n    // https://github.com/facebook/react/blob/163365a07872337e04826c4f501565d43dbd2fd4/packages/react-client/src/ReactFlightClient.js#L189-L190\n    const pendingActionQueue = new Promise((resolve, reject)=>{\n        initialServerResponse.then((initialRSCPayload)=>{\n            // setAppBuildId should be called only once, during JS initialization\n            // and before any components have hydrated.\n            (0, _appbuildid.setAppBuildId)(initialRSCPayload.b);\n            const initialTimestamp = Date.now();\n            resolve((0, _approuterinstance.createMutableActionQueue)((0, _createinitialrouterstate.createInitialRouterState)({\n                navigatedAt: initialTimestamp,\n                initialFlightData: initialRSCPayload.f,\n                initialCanonicalUrlParts: initialRSCPayload.c,\n                initialParallelRoutes: new Map(),\n                location: window.location,\n                couldBeIntercepted: initialRSCPayload.i,\n                postponed: initialRSCPayload.s,\n                prerendered: initialRSCPayload.S\n            }), instrumentationHooks));\n        }, (err)=>reject(err));\n    });\n    const reactEl = /*#__PURE__*/ (0, _jsxruntime.jsx)(StrictModeIfEnabled, {\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_headmanagercontextsharedruntime.HeadManagerContext.Provider, {\n            value: {\n                appDir: true\n            },\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Root, {\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(ServerRoot, {\n                    pendingActionQueue: pendingActionQueue\n                })\n            })\n        })\n    });\n    if (document.documentElement.id === '__next_error__') {\n        let element = reactEl;\n        // Server rendering failed, fall back to client-side rendering\n        if (true) {\n            const { createRootLevelDevOverlayElement } = __webpack_require__(/*! ./components/react-dev-overlay/app/client-entry */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/client-entry.js\");\n            // Note this won't cause hydration mismatch because we are doing CSR w/o hydration\n            element = createRootLevelDevOverlayElement(element);\n        }\n        _client.default.createRoot(appElement, reactRootOptions).render(element);\n    } else {\n        _react.default.startTransition(()=>{\n            _client.default.hydrateRoot(appElement, reactEl, {\n                ...reactRootOptions,\n                formState: initialFormStateData\n            });\n        });\n    }\n    // TODO-APP: Remove this logic when Float has GC built-in in development.\n    if (true) {\n        const { linkGc } = __webpack_require__(/*! ./app-link-gc */ \"(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js\");\n        linkGc();\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-index.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"DOMContentLoaded\");\n$RefreshReg$(_c1, \"ServerRoot\");\n$RefreshReg$(_c2, \"Root\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2FwcC1pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSw2REFBNkQ7Ozs7OzJDQTRON0NBOzs7ZUFBQUE7Ozs7OztvQkEzTlQ7b0JBRUE7b0JBQ0E7NkVBRW9COzZFQUNBO3FDQUVjOzZEQUNOO2dEQUNBO29EQUk1QjsyQ0FDb0I7aURBQ007K0NBSTFCO2dGQUNlO3NEQUVtQjsyREFDTjt3Q0FDTDtBQUU5QixnREFBZ0Q7QUFFaEQsTUFBTUMsYUFBcUNDO0FBRTNDLE1BQU1DLFVBQVUsSUFBSUM7QUFFcEIsSUFBSUMsMEJBQStEQztBQUNuRSxJQUFJQywwQkFDRkQ7QUFDRixJQUFJRSwwQkFBMEI7QUFDOUIsSUFBSUMsMkJBQTJCO0FBRS9CLElBQUlDLHVCQUFtQztBQW1CdkMsU0FBU0MsdUJBQXVCQyxHQUFrQjtJQUNoRCxJQUFJQSxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUc7UUFDaEJQLDBCQUEwQixFQUFFO0lBQzlCLE9BQU8sSUFBSU8sR0FBRyxDQUFDLEVBQUUsS0FBSyxHQUFHO1FBQ3ZCLElBQUksQ0FBQ1AseUJBQ0gsTUFBTSxxQkFBOEQsQ0FBOUQsSUFBSVEsTUFBTSxzREFBVjttQkFBQTt3QkFBQTswQkFBQTtRQUE2RDtRQUVyRSxJQUFJTix5QkFBeUI7WUFDM0JBLHdCQUF3Qk8sT0FBTyxDQUFDWCxRQUFRWSxNQUFNLENBQUNILEdBQUcsQ0FBQyxFQUFFO1FBQ3ZELE9BQU87WUFDTFAsd0JBQXdCVyxJQUFJLENBQUNKLEdBQUcsQ0FBQyxFQUFFO1FBQ3JDO0lBQ0YsT0FBTyxJQUFJQSxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUc7UUFDdkJGLHVCQUF1QkUsR0FBRyxDQUFDLEVBQUU7SUFDL0IsT0FBTyxJQUFJQSxHQUFHLENBQUMsRUFBRSxLQUFLLEdBQUc7UUFDdkIsSUFBSSxDQUFDUCx5QkFDSCxNQUFNLHFCQUE4RCxDQUE5RCxJQUFJUSxNQUFNLHNEQUFWO21CQUFBO3dCQUFBOzBCQUFBO1FBQTZEO1FBRXJFLGdEQUFnRDtRQUNoRCxNQUFNSSxlQUFlQyxLQUFLTixHQUFHLENBQUMsRUFBRTtRQUNoQyxNQUFNTyxlQUFlLElBQUlDLFdBQVdILGFBQWFJLE1BQU07UUFDdkQsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlMLGFBQWFJLE1BQU0sRUFBRUMsSUFBSztZQUM1Q0gsWUFBWSxDQUFDRyxFQUFFLEdBQUdMLGFBQWFNLFVBQVUsQ0FBQ0Q7UUFDNUM7UUFFQSxJQUFJZix5QkFBeUI7WUFDM0JBLHdCQUF3Qk8sT0FBTyxDQUFDSztRQUNsQyxPQUFPO1lBQ0xkLHdCQUF3QlcsSUFBSSxDQUFDRztRQUMvQjtJQUNGO0FBQ0Y7QUFFQSxTQUFTSywwQkFBMEJDLEdBQW9DO0lBQ3JFLDZIQUE2SDtJQUM3SCxPQUFPQSxJQUFJQyxXQUFXLEtBQUssUUFBUUQsSUFBSUMsV0FBVyxHQUFHO0FBQ3ZEO0FBRUEsNEVBQTRFO0FBQzVFLDZFQUE2RTtBQUM3RSxvRUFBb0U7QUFDcEUsc0VBQXNFO0FBQ3RFLHFEQUFxRDtBQUNyRCw0REFBNEQ7QUFDNUQsd0VBQXdFO0FBQ3hFLCtEQUErRDtBQUMvRCxTQUFTQyw2QkFBNkJGLEdBQW9DO0lBQ3hFLElBQUlwQix5QkFBeUI7UUFDM0JBLHdCQUF3QnVCLE9BQU8sQ0FBQyxDQUFDQztZQUMvQkosSUFBSVgsT0FBTyxDQUFDLE9BQU9lLFFBQVEsV0FBVzFCLFFBQVFZLE1BQU0sQ0FBQ2MsT0FBT0E7UUFDOUQ7UUFDQSxJQUFJckIsMkJBQTJCLENBQUNDLDBCQUEwQjtZQUN4RCxJQUFJZSwwQkFBMEJDLE1BQU07Z0JBQ2xDQSxJQUFJSyxLQUFLLENBQ1AscUJBRUMsQ0FGRCxJQUFJakIsTUFDRiwwSkFERjsyQkFBQTtnQ0FBQTtrQ0FBQTtnQkFFQTtZQUVKLE9BQU87Z0JBQ0xZLElBQUlNLEtBQUs7WUFDWDtZQUNBdEIsMkJBQTJCO1lBQzNCSiwwQkFBMEJDO1FBQzVCO0lBQ0Y7SUFFQUMsMEJBQTBCa0I7QUFDNUI7QUFFQSxpRkFBaUY7QUFDakYseUJBQXlCO0lBQ3ZCLElBQUlsQiwyQkFBMkIsQ0FBQ0UsMEJBQTBCO1FBQ3hERix3QkFBd0J3QixLQUFLO1FBQzdCdEIsMkJBQTJCO1FBQzNCSiwwQkFBMEJDO0lBQzVCO0lBQ0FFLDBCQUEwQjtBQUM1QjtLQVBNd0I7QUFTTixnREFBZ0Q7QUFDaEQsSUFBSTlCLFNBQVMrQixVQUFVLEtBQUssV0FBVztJQUNyQy9CLFNBQVNnQyxnQkFBZ0IsQ0FBQyxvQkFBb0JGLGtCQUFrQjtBQUNsRSxPQUFPO0lBQ0wscUVBQXFFO0lBQ3JFRyxXQUFXSDtBQUNiO0FBRUEsTUFBTUksOEJBQStCQyxLQUFLQyxRQUFRLEdBQUdELEtBQUtDLFFBQVEsSUFBSSxFQUFFO0FBQ3hFRiw0QkFBNEJSLE9BQU8sQ0FBQ2pCO0FBQ3BDeUIsNEJBQTRCcEIsSUFBSSxHQUFHTDtBQUVuQyxNQUFNNEIsV0FBVyxJQUFJQyxlQUFlO0lBQ2xDQyxPQUFNQyxVQUFVO1FBQ2RmLDZCQUE2QmU7SUFDL0I7QUFDRjtBQUVBLE1BQU1DLHdCQUF3QkMsQ0FBQUEsR0FBQUEsU0FBQUEsd0JBQUFBLEVBQzVCTCxVQUNBO0lBQUVNLFlBQUFBLGVBQUFBLFVBQVU7SUFBRUMsa0JBQUFBLHFCQUFBQSxnQkFBZ0I7QUFBQztBQUdqQyxvQkFBb0IsS0FJbkI7SUFKbUIsTUFDbEJFLGtCQUFrQixFQUduQixHQUptQjtJQUtsQixNQUFNQyxvQkFBb0JDLENBQUFBLEdBQUFBLE9BQUFBLEdBQUFBLEVBQUlQO0lBQzlCLE1BQU1RLGNBQWNELENBQUFBLEdBQUFBLE9BQUFBLEdBQUcsRUFBdUJGO0lBRTlDLE1BQU1JLFNBQUFBLFdBQUFBLEdBQ0oscUJBQUNDLFdBQUFBLE9BQVM7UUFDUkYsYUFBYUE7UUFDYkcsK0JBQStCTCxrQkFBa0JNLENBQUM7UUFDbERDLGFBQWFQLGtCQUFrQlEsQ0FBQzs7SUFJcEMsSUFuQmNYLEtBbUJVLElBQXNCRyxrQkFBa0JZLENBQUMsRUFBRTtRQUNqRSxvRkFBb0Y7UUFDcEYsZ0ZBQWdGO1FBQ2hGLHFCQUNFLHFCQUFDQywrQkFBQUEsa0JBQWtCO1lBQUNDLE9BQU9kLGtCQUFrQlksQ0FBQztzQkFDM0NUOztJQUdQO0lBRUEsT0FBT0E7QUFDVDtNQTNCU0w7QUE2QlQsTUFBTWlCLHNCQUFzQk4sS0FBa0MsR0FDMURRLE9BQUFBLE9BQUssQ0FBQ0MsVUFBVSxHQUNoQkQsQ0FBYztBQUVsQixjQUFjLEtBQXlDO0lBQXpDLE1BQUVJLFFBQVEsRUFBK0IsR0FBekM7SUFDWixJQUFJWixLQUE0QixFQUFFLEVBT2pDO0lBRUQsT0FBT1k7QUFDVDtNQVhTRDtBQWFULE1BQU1VLG1CQUErQztJQUNuREMsb0JBQUFBLG9CQUFBQSxrQkFBa0I7SUFDbEJDLGVBQUFBLHdCQUFBQSxhQUFhO0lBQ2JDLGlCQUFBQSx3QkFBQUEsZUFBZTtBQUNqQjtBQVNPLFNBQVNsRixRQUNkbUYsb0JBQXVEO0lBRXZELGtFQUFrRTtJQUNsRSxvRUFBb0U7SUFDcEUsd0NBQXdDO0lBQ3hDLDJJQUEySTtJQUMzSSxNQUFNbkMscUJBQW9ELElBQUlvQyxRQUM1RCxDQUFDQyxTQUFTQztRQUNSM0Msc0JBQXNCNEMsSUFBSSxDQUN4QixDQUFDdEM7WUFDQyxxRUFBcUU7WUFDckUsMkNBQTJDO1lBQzNDdUMsQ0FBQUEsR0FBQUEsWUFBQUEsYUFBQUEsRUFBY3ZDLGtCQUFrQndDLENBQUM7WUFFakMsTUFBTUMsbUJBQW1CQyxLQUFLZCxHQUFHO1lBRWpDUSxRQUNFTyxDQUFBQSxHQUFBQSxtQkFBQUEsd0JBQUFBLEVBQ0VDLENBQUFBLEdBQUFBLDBCQUFBQSx3QkFBQUEsRUFBeUI7Z0JBQ3ZCQyxhQUFhSjtnQkFDYkssbUJBQW1COUMsa0JBQWtCK0MsQ0FBQztnQkFDdENDLDBCQUEwQmhELGtCQUFrQmlELENBQUM7Z0JBQzdDQyx1QkFBdUIsSUFBSUM7Z0JBQzNCQyxVQUFVNUIsT0FBTzRCLFFBQVE7Z0JBQ3pCQyxvQkFBb0JyRCxrQkFBa0IzQixDQUFDO2dCQUN2Q2lGLFdBQVd0RCxrQkFBa0J1RCxDQUFDO2dCQUM5QkMsYUFBYXhELGtCQUFrQnlELENBQUM7WUFDbEMsSUFDQXZCO1FBR04sR0FDQSxDQUFDd0IsTUFBZXJCLE9BQU9xQjtJQUUzQjtJQUdGLE1BQU1DLFVBQUFBLFdBQUFBLEdBQ0oscUJBQUM1QyxxQkFBQUE7a0JBQ0MsbUNBQUM2QyxpQ0FBQUEsa0JBQWtCLENBQUNDLFFBQVE7WUFBQy9DLE9BQU87Z0JBQUVnRCxRQUFRO1lBQUs7c0JBQ2pELG1DQUFDMUMsTUFBQUE7MEJBQ0MsbUNBQUN0QixZQUFBQTtvQkFBV0Msb0JBQW9CQTs7Ozs7SUFNeEMsSUFBSTlDLFNBQVM4RyxlQUFlLENBQUNDLEVBQUUsS0FBSyxrQkFBa0I7UUFDcEQsSUFBSUMsVUFBVU47UUFDZCw4REFBOEQ7UUFDOUQsSUFBSWxELElBQW9CLEVBQW1CO1lBQ3pDLE1BQU0sRUFBRXlELGdDQUFnQyxFQUFFLEdBQ3hDQyxtQkFBT0EsQ0FBQyw2SkFBaUQ7WUFFM0Qsa0ZBQWtGO1lBQ2xGRixVQUFVQyxpQ0FBaUNEO1FBQzdDO1FBRUFHLFFBQUFBLE9BQWMsQ0FBQ0MsVUFBVSxDQUFDckgsWUFBWThFLGtCQUFrQndDLE1BQU0sQ0FBQ0w7SUFDakUsT0FBTztRQUNMaEQsT0FBQUEsT0FBSyxDQUFDc0QsZUFBZSxDQUFDO1lBQ3BCSCxRQUFBQSxPQUFjLENBQUNJLFdBQVcsQ0FBQ3hILFlBQVkyRyxTQUFTO2dCQUM5QyxHQUFHN0IsZ0JBQWdCO2dCQUNuQjJDLFdBQVdoSDtZQUNiO1FBQ0Y7SUFDRjtJQUVBLHlFQUF5RTtJQUN6RSxJQXBJY29DLElBb0lVLEVBQW1CO1FBQ3pDLE1BQU0sRUFBRTZFLE1BQU0sRUFBRSxHQUNkUCxtQkFBT0EsQ0FBQyx5RkFBZTtRQUN6Qk87SUFDRjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXHNyY1xcY2xpZW50XFxhcHAtaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIGltcG9ydHMgcG9seWZpbGwgZnJvbSBgQG5leHQvcG9seWZpbGwtbW9kdWxlYCBhZnRlciBidWlsZC5cbmltcG9ydCAnLi4vYnVpbGQvcG9seWZpbGxzL3BvbHlmaWxsLW1vZHVsZSdcblxuaW1wb3J0ICcuL2NvbXBvbmVudHMvZ2xvYmFscy9wYXRjaC1jb25zb2xlJ1xuaW1wb3J0ICcuL2NvbXBvbmVudHMvZ2xvYmFscy9oYW5kbGUtZ2xvYmFsLWVycm9ycydcblxuaW1wb3J0IFJlYWN0RE9NQ2xpZW50IGZyb20gJ3JlYWN0LWRvbS9jbGllbnQnXG5pbXBvcnQgUmVhY3QsIHsgdXNlIH0gZnJvbSAncmVhY3QnXG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgaW1wb3J0L25vLWV4dHJhbmVvdXMtZGVwZW5kZW5jaWVzXG5pbXBvcnQgeyBjcmVhdGVGcm9tUmVhZGFibGVTdHJlYW0gfSBmcm9tICdyZWFjdC1zZXJ2ZXItZG9tLXdlYnBhY2svY2xpZW50J1xuaW1wb3J0IHsgSGVhZE1hbmFnZXJDb250ZXh0IH0gZnJvbSAnLi4vc2hhcmVkL2xpYi9oZWFkLW1hbmFnZXItY29udGV4dC5zaGFyZWQtcnVudGltZSdcbmltcG9ydCB7IG9uUmVjb3ZlcmFibGVFcnJvciB9IGZyb20gJy4vcmVhY3QtY2xpZW50LWNhbGxiYWNrcy9vbi1yZWNvdmVyYWJsZS1lcnJvcidcbmltcG9ydCB7XG4gIG9uQ2F1Z2h0RXJyb3IsXG4gIG9uVW5jYXVnaHRFcnJvcixcbn0gZnJvbSAnLi9yZWFjdC1jbGllbnQtY2FsbGJhY2tzL2Vycm9yLWJvdW5kYXJ5LWNhbGxiYWNrcydcbmltcG9ydCB7IGNhbGxTZXJ2ZXIgfSBmcm9tICcuL2FwcC1jYWxsLXNlcnZlcidcbmltcG9ydCB7IGZpbmRTb3VyY2VNYXBVUkwgfSBmcm9tICcuL2FwcC1maW5kLXNvdXJjZS1tYXAtdXJsJ1xuaW1wb3J0IHtcbiAgdHlwZSBBcHBSb3V0ZXJBY3Rpb25RdWV1ZSxcbiAgY3JlYXRlTXV0YWJsZUFjdGlvblF1ZXVlLFxufSBmcm9tICcuL2NvbXBvbmVudHMvYXBwLXJvdXRlci1pbnN0YW5jZSdcbmltcG9ydCBBcHBSb3V0ZXIgZnJvbSAnLi9jb21wb25lbnRzL2FwcC1yb3V0ZXInXG5pbXBvcnQgdHlwZSB7IEluaXRpYWxSU0NQYXlsb2FkIH0gZnJvbSAnLi4vc2VydmVyL2FwcC1yZW5kZXIvdHlwZXMnXG5pbXBvcnQgeyBjcmVhdGVJbml0aWFsUm91dGVyU3RhdGUgfSBmcm9tICcuL2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvY3JlYXRlLWluaXRpYWwtcm91dGVyLXN0YXRlJ1xuaW1wb3J0IHsgTWlzc2luZ1Nsb3RDb250ZXh0IH0gZnJvbSAnLi4vc2hhcmVkL2xpYi9hcHAtcm91dGVyLWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUnXG5pbXBvcnQgeyBzZXRBcHBCdWlsZElkIH0gZnJvbSAnLi9hcHAtYnVpbGQtaWQnXG5cbi8vLyA8cmVmZXJlbmNlIHR5cGVzPVwicmVhY3QtZG9tL2V4cGVyaW1lbnRhbFwiIC8+XG5cbmNvbnN0IGFwcEVsZW1lbnQ6IEhUTUxFbGVtZW50IHwgRG9jdW1lbnQgPSBkb2N1bWVudFxuXG5jb25zdCBlbmNvZGVyID0gbmV3IFRleHRFbmNvZGVyKClcblxubGV0IGluaXRpYWxTZXJ2ZXJEYXRhQnVmZmVyOiAoc3RyaW5nIHwgVWludDhBcnJheSlbXSB8IHVuZGVmaW5lZCA9IHVuZGVmaW5lZFxubGV0IGluaXRpYWxTZXJ2ZXJEYXRhV3JpdGVyOiBSZWFkYWJsZVN0cmVhbURlZmF1bHRDb250cm9sbGVyIHwgdW5kZWZpbmVkID1cbiAgdW5kZWZpbmVkXG5sZXQgaW5pdGlhbFNlcnZlckRhdGFMb2FkZWQgPSBmYWxzZVxubGV0IGluaXRpYWxTZXJ2ZXJEYXRhRmx1c2hlZCA9IGZhbHNlXG5cbmxldCBpbml0aWFsRm9ybVN0YXRlRGF0YTogbnVsbCB8IGFueSA9IG51bGxcblxudHlwZSBGbGlnaHRTZWdtZW50ID1cbiAgfCBbaXNCb290U3RyYXA6IDBdXG4gIHwgW2lzTm90Qm9vdHN0cmFwOiAxLCByZXNwb25zZVBhcnRpYWw6IHN0cmluZ11cbiAgfCBbaXNGb3JtU3RhdGU6IDIsIGZvcm1TdGF0ZTogYW55XVxuICB8IFtpc0JpbmFyeTogMywgcmVzcG9uc2VCYXNlNjRQYXJ0aWFsOiBzdHJpbmddXG5cbnR5cGUgTmV4dEZsaWdodCA9IE9taXQ8QXJyYXk8RmxpZ2h0U2VnbWVudD4sICdwdXNoJz4gJiB7XG4gIHB1c2g6IChzZWc6IEZsaWdodFNlZ21lbnQpID0+IHZvaWRcbn1cblxuZGVjbGFyZSBnbG9iYWwge1xuICAvLyBJZiB5b3UncmUgd29ya2luZyBpbiBhIGJyb3dzZXIgZW52aXJvbm1lbnRcbiAgaW50ZXJmYWNlIFdpbmRvdyB7XG4gICAgX19uZXh0X2Y6IE5leHRGbGlnaHRcbiAgfVxufVxuXG5mdW5jdGlvbiBuZXh0U2VydmVyRGF0YUNhbGxiYWNrKHNlZzogRmxpZ2h0U2VnbWVudCk6IHZvaWQge1xuICBpZiAoc2VnWzBdID09PSAwKSB7XG4gICAgaW5pdGlhbFNlcnZlckRhdGFCdWZmZXIgPSBbXVxuICB9IGVsc2UgaWYgKHNlZ1swXSA9PT0gMSkge1xuICAgIGlmICghaW5pdGlhbFNlcnZlckRhdGFCdWZmZXIpXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VuZXhwZWN0ZWQgc2VydmVyIGRhdGE6IG1pc3NpbmcgYm9vdHN0cmFwIHNjcmlwdC4nKVxuXG4gICAgaWYgKGluaXRpYWxTZXJ2ZXJEYXRhV3JpdGVyKSB7XG4gICAgICBpbml0aWFsU2VydmVyRGF0YVdyaXRlci5lbnF1ZXVlKGVuY29kZXIuZW5jb2RlKHNlZ1sxXSkpXG4gICAgfSBlbHNlIHtcbiAgICAgIGluaXRpYWxTZXJ2ZXJEYXRhQnVmZmVyLnB1c2goc2VnWzFdKVxuICAgIH1cbiAgfSBlbHNlIGlmIChzZWdbMF0gPT09IDIpIHtcbiAgICBpbml0aWFsRm9ybVN0YXRlRGF0YSA9IHNlZ1sxXVxuICB9IGVsc2UgaWYgKHNlZ1swXSA9PT0gMykge1xuICAgIGlmICghaW5pdGlhbFNlcnZlckRhdGFCdWZmZXIpXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VuZXhwZWN0ZWQgc2VydmVyIGRhdGE6IG1pc3NpbmcgYm9vdHN0cmFwIHNjcmlwdC4nKVxuXG4gICAgLy8gRGVjb2RlIHRoZSBiYXNlNjQgc3RyaW5nIGJhY2sgdG8gYmluYXJ5IGRhdGEuXG4gICAgY29uc3QgYmluYXJ5U3RyaW5nID0gYXRvYihzZWdbMV0pXG4gICAgY29uc3QgZGVjb2RlZENodW5rID0gbmV3IFVpbnQ4QXJyYXkoYmluYXJ5U3RyaW5nLmxlbmd0aClcbiAgICBmb3IgKHZhciBpID0gMDsgaSA8IGJpbmFyeVN0cmluZy5sZW5ndGg7IGkrKykge1xuICAgICAgZGVjb2RlZENodW5rW2ldID0gYmluYXJ5U3RyaW5nLmNoYXJDb2RlQXQoaSlcbiAgICB9XG5cbiAgICBpZiAoaW5pdGlhbFNlcnZlckRhdGFXcml0ZXIpIHtcbiAgICAgIGluaXRpYWxTZXJ2ZXJEYXRhV3JpdGVyLmVucXVldWUoZGVjb2RlZENodW5rKVxuICAgIH0gZWxzZSB7XG4gICAgICBpbml0aWFsU2VydmVyRGF0YUJ1ZmZlci5wdXNoKGRlY29kZWRDaHVuaylcbiAgICB9XG4gIH1cbn1cblxuZnVuY3Rpb24gaXNTdHJlYW1FcnJvck9yVW5maW5pc2hlZChjdHI6IFJlYWRhYmxlU3RyZWFtRGVmYXVsdENvbnRyb2xsZXIpIHtcbiAgLy8gSWYgYGRlc2lyZWRTaXplYCBpcyBudWxsLCBpdCBtZWFucyB0aGUgc3RyZWFtIGlzIGNsb3NlZCBvciBlcnJvcmVkLiBJZiBpdCBpcyBsb3dlciB0aGFuIDAsIHRoZSBzdHJlYW0gaXMgc3RpbGwgdW5maW5pc2hlZC5cbiAgcmV0dXJuIGN0ci5kZXNpcmVkU2l6ZSA9PT0gbnVsbCB8fCBjdHIuZGVzaXJlZFNpemUgPCAwXG59XG5cbi8vIFRoZXJlIG1pZ2h0IGJlIHJhY2UgY29uZGl0aW9ucyBiZXR3ZWVuIGBuZXh0U2VydmVyRGF0YVJlZ2lzdGVyV3JpdGVyYCBhbmRcbi8vIGBET01Db250ZW50TG9hZGVkYC4gVGhlIGZvcm1lciB3aWxsIGJlIGNhbGxlZCB3aGVuIFJlYWN0IHN0YXJ0cyB0byBoeWRyYXRlXG4vLyB0aGUgcm9vdCwgdGhlIGxhdHRlciB3aWxsIGJlIGNhbGxlZCB3aGVuIHRoZSBET00gaXMgZnVsbHkgbG9hZGVkLlxuLy8gRm9yIHN0cmVhbWluZywgdGhlIGZvcm1lciBpcyBjYWxsZWQgZmlyc3QgZHVlIHRvIHBhcnRpYWwgaHlkcmF0aW9uLlxuLy8gRm9yIG5vbi1zdHJlYW1pbmcsIHRoZSBsYXR0ZXIgY2FuIGJlIGNhbGxlZCBmaXJzdC5cbi8vIEhlbmNlLCB3ZSB1c2UgdHdvIHZhcmlhYmxlcyBgaW5pdGlhbFNlcnZlckRhdGFMb2FkZWRgIGFuZFxuLy8gYGluaXRpYWxTZXJ2ZXJEYXRhRmx1c2hlZGAgdG8gbWFrZSBzdXJlIHRoZSB3cml0ZXIgd2lsbCBiZSBjbG9zZWQgYW5kXG4vLyBgaW5pdGlhbFNlcnZlckRhdGFCdWZmZXJgIHdpbGwgYmUgY2xlYXJlZCBpbiB0aGUgcmlnaHQgdGltZS5cbmZ1bmN0aW9uIG5leHRTZXJ2ZXJEYXRhUmVnaXN0ZXJXcml0ZXIoY3RyOiBSZWFkYWJsZVN0cmVhbURlZmF1bHRDb250cm9sbGVyKSB7XG4gIGlmIChpbml0aWFsU2VydmVyRGF0YUJ1ZmZlcikge1xuICAgIGluaXRpYWxTZXJ2ZXJEYXRhQnVmZmVyLmZvckVhY2goKHZhbCkgPT4ge1xuICAgICAgY3RyLmVucXVldWUodHlwZW9mIHZhbCA9PT0gJ3N0cmluZycgPyBlbmNvZGVyLmVuY29kZSh2YWwpIDogdmFsKVxuICAgIH0pXG4gICAgaWYgKGluaXRpYWxTZXJ2ZXJEYXRhTG9hZGVkICYmICFpbml0aWFsU2VydmVyRGF0YUZsdXNoZWQpIHtcbiAgICAgIGlmIChpc1N0cmVhbUVycm9yT3JVbmZpbmlzaGVkKGN0cikpIHtcbiAgICAgICAgY3RyLmVycm9yKFxuICAgICAgICAgIG5ldyBFcnJvcihcbiAgICAgICAgICAgICdUaGUgY29ubmVjdGlvbiB0byB0aGUgcGFnZSB3YXMgdW5leHBlY3RlZGx5IGNsb3NlZCwgcG9zc2libHkgZHVlIHRvIHRoZSBzdG9wIGJ1dHRvbiBiZWluZyBjbGlja2VkLCBsb3NzIG9mIFdpLUZpLCBvciBhbiB1bnN0YWJsZSBpbnRlcm5ldCBjb25uZWN0aW9uLidcbiAgICAgICAgICApXG4gICAgICAgIClcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGN0ci5jbG9zZSgpXG4gICAgICB9XG4gICAgICBpbml0aWFsU2VydmVyRGF0YUZsdXNoZWQgPSB0cnVlXG4gICAgICBpbml0aWFsU2VydmVyRGF0YUJ1ZmZlciA9IHVuZGVmaW5lZFxuICAgIH1cbiAgfVxuXG4gIGluaXRpYWxTZXJ2ZXJEYXRhV3JpdGVyID0gY3RyXG59XG5cbi8vIFdoZW4gYERPTUNvbnRlbnRMb2FkZWRgLCB3ZSBjYW4gY2xvc2UgYWxsIHBlbmRpbmcgd3JpdGVycyB0byBmaW5pc2ggaHlkcmF0aW9uLlxuY29uc3QgRE9NQ29udGVudExvYWRlZCA9IGZ1bmN0aW9uICgpIHtcbiAgaWYgKGluaXRpYWxTZXJ2ZXJEYXRhV3JpdGVyICYmICFpbml0aWFsU2VydmVyRGF0YUZsdXNoZWQpIHtcbiAgICBpbml0aWFsU2VydmVyRGF0YVdyaXRlci5jbG9zZSgpXG4gICAgaW5pdGlhbFNlcnZlckRhdGFGbHVzaGVkID0gdHJ1ZVxuICAgIGluaXRpYWxTZXJ2ZXJEYXRhQnVmZmVyID0gdW5kZWZpbmVkXG4gIH1cbiAgaW5pdGlhbFNlcnZlckRhdGFMb2FkZWQgPSB0cnVlXG59XG5cbi8vIEl0J3MgcG9zc2libGUgdGhhdCB0aGUgRE9NIGlzIGFscmVhZHkgbG9hZGVkLlxuaWYgKGRvY3VtZW50LnJlYWR5U3RhdGUgPT09ICdsb2FkaW5nJykge1xuICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdET01Db250ZW50TG9hZGVkJywgRE9NQ29udGVudExvYWRlZCwgZmFsc2UpXG59IGVsc2Uge1xuICAvLyBEZWxheWVkIGluIG1hcmNvIHRhc2sgdG8gZW5zdXJlIGl0J3MgZXhlY3V0ZWQgbGF0ZXIgdGhhbiBoeWRyYXRpb25cbiAgc2V0VGltZW91dChET01Db250ZW50TG9hZGVkKVxufVxuXG5jb25zdCBuZXh0U2VydmVyRGF0YUxvYWRpbmdHbG9iYWwgPSAoc2VsZi5fX25leHRfZiA9IHNlbGYuX19uZXh0X2YgfHwgW10pXG5uZXh0U2VydmVyRGF0YUxvYWRpbmdHbG9iYWwuZm9yRWFjaChuZXh0U2VydmVyRGF0YUNhbGxiYWNrKVxubmV4dFNlcnZlckRhdGFMb2FkaW5nR2xvYmFsLnB1c2ggPSBuZXh0U2VydmVyRGF0YUNhbGxiYWNrXG5cbmNvbnN0IHJlYWRhYmxlID0gbmV3IFJlYWRhYmxlU3RyZWFtKHtcbiAgc3RhcnQoY29udHJvbGxlcikge1xuICAgIG5leHRTZXJ2ZXJEYXRhUmVnaXN0ZXJXcml0ZXIoY29udHJvbGxlcilcbiAgfSxcbn0pXG5cbmNvbnN0IGluaXRpYWxTZXJ2ZXJSZXNwb25zZSA9IGNyZWF0ZUZyb21SZWFkYWJsZVN0cmVhbTxJbml0aWFsUlNDUGF5bG9hZD4oXG4gIHJlYWRhYmxlLFxuICB7IGNhbGxTZXJ2ZXIsIGZpbmRTb3VyY2VNYXBVUkwgfVxuKVxuXG5mdW5jdGlvbiBTZXJ2ZXJSb290KHtcbiAgcGVuZGluZ0FjdGlvblF1ZXVlLFxufToge1xuICBwZW5kaW5nQWN0aW9uUXVldWU6IFByb21pc2U8QXBwUm91dGVyQWN0aW9uUXVldWU+XG59KTogUmVhY3QuUmVhY3ROb2RlIHtcbiAgY29uc3QgaW5pdGlhbFJTQ1BheWxvYWQgPSB1c2UoaW5pdGlhbFNlcnZlclJlc3BvbnNlKVxuICBjb25zdCBhY3Rpb25RdWV1ZSA9IHVzZTxBcHBSb3V0ZXJBY3Rpb25RdWV1ZT4ocGVuZGluZ0FjdGlvblF1ZXVlKVxuXG4gIGNvbnN0IHJvdXRlciA9IChcbiAgICA8QXBwUm91dGVyXG4gICAgICBhY3Rpb25RdWV1ZT17YWN0aW9uUXVldWV9XG4gICAgICBnbG9iYWxFcnJvckNvbXBvbmVudEFuZFN0eWxlcz17aW5pdGlhbFJTQ1BheWxvYWQuR31cbiAgICAgIGFzc2V0UHJlZml4PXtpbml0aWFsUlNDUGF5bG9hZC5wfVxuICAgIC8+XG4gIClcblxuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcgJiYgaW5pdGlhbFJTQ1BheWxvYWQubSkge1xuICAgIC8vIFdlIHByb3ZpZGUgbWlzc2luZyBzbG90IGluZm9ybWF0aW9uIGluIGEgY29udGV4dCBwcm92aWRlciBvbmx5IGR1cmluZyBkZXZlbG9wbWVudFxuICAgIC8vIGFzIHdlIGxvZyBzb21lIGFkZGl0aW9uYWwgaW5mb3JtYXRpb24gYWJvdXQgdGhlIG1pc3Npbmcgc2xvdHMgaW4gdGhlIGNvbnNvbGUuXG4gICAgcmV0dXJuIChcbiAgICAgIDxNaXNzaW5nU2xvdENvbnRleHQgdmFsdWU9e2luaXRpYWxSU0NQYXlsb2FkLm19PlxuICAgICAgICB7cm91dGVyfVxuICAgICAgPC9NaXNzaW5nU2xvdENvbnRleHQ+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIHJvdXRlclxufVxuXG5jb25zdCBTdHJpY3RNb2RlSWZFbmFibGVkID0gcHJvY2Vzcy5lbnYuX19ORVhUX1NUUklDVF9NT0RFX0FQUFxuICA/IFJlYWN0LlN0cmljdE1vZGVcbiAgOiBSZWFjdC5GcmFnbWVudFxuXG5mdW5jdGlvbiBSb290KHsgY2hpbGRyZW4gfTogUmVhY3QuUHJvcHNXaXRoQ2hpbGRyZW48e30+KSB7XG4gIGlmIChwcm9jZXNzLmVudi5fX05FWFRfVEVTVF9NT0RFKSB7XG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL3J1bGVzLW9mLWhvb2tzXG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIHdpbmRvdy5fX05FWFRfSFlEUkFURUQgPSB0cnVlXG4gICAgICB3aW5kb3cuX19ORVhUX0hZRFJBVEVEX0FUID0gcGVyZm9ybWFuY2Uubm93KClcbiAgICAgIHdpbmRvdy5fX05FWFRfSFlEUkFURURfQ0I/LigpXG4gICAgfSwgW10pXG4gIH1cblxuICByZXR1cm4gY2hpbGRyZW5cbn1cblxuY29uc3QgcmVhY3RSb290T3B0aW9uczogUmVhY3RET01DbGllbnQuUm9vdE9wdGlvbnMgPSB7XG4gIG9uUmVjb3ZlcmFibGVFcnJvcixcbiAgb25DYXVnaHRFcnJvcixcbiAgb25VbmNhdWdodEVycm9yLFxufVxuXG5leHBvcnQgdHlwZSBDbGllbnRJbnN0cnVtZW50YXRpb25Ib29rcyA9IHtcbiAgb25Sb3V0ZXJUcmFuc2l0aW9uU3RhcnQ/OiAoXG4gICAgdXJsOiBzdHJpbmcsXG4gICAgbmF2aWdhdGlvblR5cGU6ICdwdXNoJyB8ICdyZXBsYWNlJyB8ICd0cmF2ZXJzZSdcbiAgKSA9PiB2b2lkXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBoeWRyYXRlKFxuICBpbnN0cnVtZW50YXRpb25Ib29rczogQ2xpZW50SW5zdHJ1bWVudGF0aW9uSG9va3MgfCBudWxsXG4pIHtcbiAgLy8gUmVhY3Qgb3ZlcnJpZGVzIGAudGhlbmAgYW5kIGRvZXNuJ3QgcmV0dXJuIGEgbmV3IHByb21pc2UgY2hhaW4sXG4gIC8vIHNvIHdlIHdyYXAgdGhlIGFjdGlvbiBxdWV1ZSBpbiBhIHByb21pc2UgdG8gZW5zdXJlIHRoYXQgaXRzIHZhbHVlXG4gIC8vIGlzIGRlZmluZWQgd2hlbiB0aGUgcHJvbWlzZSByZXNvbHZlcy5cbiAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0L2Jsb2IvMTYzMzY1YTA3ODcyMzM3ZTA0ODI2YzRmNTAxNTY1ZDQzZGJkMmZkNC9wYWNrYWdlcy9yZWFjdC1jbGllbnQvc3JjL1JlYWN0RmxpZ2h0Q2xpZW50LmpzI0wxODktTDE5MFxuICBjb25zdCBwZW5kaW5nQWN0aW9uUXVldWU6IFByb21pc2U8QXBwUm91dGVyQWN0aW9uUXVldWU+ID0gbmV3IFByb21pc2UoXG4gICAgKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgaW5pdGlhbFNlcnZlclJlc3BvbnNlLnRoZW4oXG4gICAgICAgIChpbml0aWFsUlNDUGF5bG9hZCkgPT4ge1xuICAgICAgICAgIC8vIHNldEFwcEJ1aWxkSWQgc2hvdWxkIGJlIGNhbGxlZCBvbmx5IG9uY2UsIGR1cmluZyBKUyBpbml0aWFsaXphdGlvblxuICAgICAgICAgIC8vIGFuZCBiZWZvcmUgYW55IGNvbXBvbmVudHMgaGF2ZSBoeWRyYXRlZC5cbiAgICAgICAgICBzZXRBcHBCdWlsZElkKGluaXRpYWxSU0NQYXlsb2FkLmIpXG5cbiAgICAgICAgICBjb25zdCBpbml0aWFsVGltZXN0YW1wID0gRGF0ZS5ub3coKVxuXG4gICAgICAgICAgcmVzb2x2ZShcbiAgICAgICAgICAgIGNyZWF0ZU11dGFibGVBY3Rpb25RdWV1ZShcbiAgICAgICAgICAgICAgY3JlYXRlSW5pdGlhbFJvdXRlclN0YXRlKHtcbiAgICAgICAgICAgICAgICBuYXZpZ2F0ZWRBdDogaW5pdGlhbFRpbWVzdGFtcCxcbiAgICAgICAgICAgICAgICBpbml0aWFsRmxpZ2h0RGF0YTogaW5pdGlhbFJTQ1BheWxvYWQuZixcbiAgICAgICAgICAgICAgICBpbml0aWFsQ2Fub25pY2FsVXJsUGFydHM6IGluaXRpYWxSU0NQYXlsb2FkLmMsXG4gICAgICAgICAgICAgICAgaW5pdGlhbFBhcmFsbGVsUm91dGVzOiBuZXcgTWFwKCksXG4gICAgICAgICAgICAgICAgbG9jYXRpb246IHdpbmRvdy5sb2NhdGlvbixcbiAgICAgICAgICAgICAgICBjb3VsZEJlSW50ZXJjZXB0ZWQ6IGluaXRpYWxSU0NQYXlsb2FkLmksXG4gICAgICAgICAgICAgICAgcG9zdHBvbmVkOiBpbml0aWFsUlNDUGF5bG9hZC5zLFxuICAgICAgICAgICAgICAgIHByZXJlbmRlcmVkOiBpbml0aWFsUlNDUGF5bG9hZC5TLFxuICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgICAgaW5zdHJ1bWVudGF0aW9uSG9va3NcbiAgICAgICAgICAgIClcbiAgICAgICAgICApXG4gICAgICAgIH0sXG4gICAgICAgIChlcnI6IEVycm9yKSA9PiByZWplY3QoZXJyKVxuICAgICAgKVxuICAgIH1cbiAgKVxuXG4gIGNvbnN0IHJlYWN0RWwgPSAoXG4gICAgPFN0cmljdE1vZGVJZkVuYWJsZWQ+XG4gICAgICA8SGVhZE1hbmFnZXJDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt7IGFwcERpcjogdHJ1ZSB9fT5cbiAgICAgICAgPFJvb3Q+XG4gICAgICAgICAgPFNlcnZlclJvb3QgcGVuZGluZ0FjdGlvblF1ZXVlPXtwZW5kaW5nQWN0aW9uUXVldWV9IC8+XG4gICAgICAgIDwvUm9vdD5cbiAgICAgIDwvSGVhZE1hbmFnZXJDb250ZXh0LlByb3ZpZGVyPlxuICAgIDwvU3RyaWN0TW9kZUlmRW5hYmxlZD5cbiAgKVxuXG4gIGlmIChkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuaWQgPT09ICdfX25leHRfZXJyb3JfXycpIHtcbiAgICBsZXQgZWxlbWVudCA9IHJlYWN0RWxcbiAgICAvLyBTZXJ2ZXIgcmVuZGVyaW5nIGZhaWxlZCwgZmFsbCBiYWNrIHRvIGNsaWVudC1zaWRlIHJlbmRlcmluZ1xuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICBjb25zdCB7IGNyZWF0ZVJvb3RMZXZlbERldk92ZXJsYXlFbGVtZW50IH0gPVxuICAgICAgICByZXF1aXJlKCcuL2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvYXBwL2NsaWVudC1lbnRyeScpIGFzIHR5cGVvZiBpbXBvcnQoJy4vY29tcG9uZW50cy9yZWFjdC1kZXYtb3ZlcmxheS9hcHAvY2xpZW50LWVudHJ5JylcblxuICAgICAgLy8gTm90ZSB0aGlzIHdvbid0IGNhdXNlIGh5ZHJhdGlvbiBtaXNtYXRjaCBiZWNhdXNlIHdlIGFyZSBkb2luZyBDU1Igdy9vIGh5ZHJhdGlvblxuICAgICAgZWxlbWVudCA9IGNyZWF0ZVJvb3RMZXZlbERldk92ZXJsYXlFbGVtZW50KGVsZW1lbnQpXG4gICAgfVxuXG4gICAgUmVhY3RET01DbGllbnQuY3JlYXRlUm9vdChhcHBFbGVtZW50LCByZWFjdFJvb3RPcHRpb25zKS5yZW5kZXIoZWxlbWVudClcbiAgfSBlbHNlIHtcbiAgICBSZWFjdC5zdGFydFRyYW5zaXRpb24oKCkgPT4ge1xuICAgICAgUmVhY3RET01DbGllbnQuaHlkcmF0ZVJvb3QoYXBwRWxlbWVudCwgcmVhY3RFbCwge1xuICAgICAgICAuLi5yZWFjdFJvb3RPcHRpb25zLFxuICAgICAgICBmb3JtU3RhdGU6IGluaXRpYWxGb3JtU3RhdGVEYXRhLFxuICAgICAgfSlcbiAgICB9KVxuICB9XG5cbiAgLy8gVE9ETy1BUFA6IFJlbW92ZSB0aGlzIGxvZ2ljIHdoZW4gRmxvYXQgaGFzIEdDIGJ1aWx0LWluIGluIGRldmVsb3BtZW50LlxuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgIGNvbnN0IHsgbGlua0djIH0gPVxuICAgICAgcmVxdWlyZSgnLi9hcHAtbGluay1nYycpIGFzIHR5cGVvZiBpbXBvcnQoJy4vYXBwLWxpbmstZ2MnKVxuICAgIGxpbmtHYygpXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJoeWRyYXRlIiwiYXBwRWxlbWVudCIsImRvY3VtZW50IiwiZW5jb2RlciIsIlRleHRFbmNvZGVyIiwiaW5pdGlhbFNlcnZlckRhdGFCdWZmZXIiLCJ1bmRlZmluZWQiLCJpbml0aWFsU2VydmVyRGF0YVdyaXRlciIsImluaXRpYWxTZXJ2ZXJEYXRhTG9hZGVkIiwiaW5pdGlhbFNlcnZlckRhdGFGbHVzaGVkIiwiaW5pdGlhbEZvcm1TdGF0ZURhdGEiLCJuZXh0U2VydmVyRGF0YUNhbGxiYWNrIiwic2VnIiwiRXJyb3IiLCJlbnF1ZXVlIiwiZW5jb2RlIiwicHVzaCIsImJpbmFyeVN0cmluZyIsImF0b2IiLCJkZWNvZGVkQ2h1bmsiLCJVaW50OEFycmF5IiwibGVuZ3RoIiwiaSIsImNoYXJDb2RlQXQiLCJpc1N0cmVhbUVycm9yT3JVbmZpbmlzaGVkIiwiY3RyIiwiZGVzaXJlZFNpemUiLCJuZXh0U2VydmVyRGF0YVJlZ2lzdGVyV3JpdGVyIiwiZm9yRWFjaCIsInZhbCIsImVycm9yIiwiY2xvc2UiLCJET01Db250ZW50TG9hZGVkIiwicmVhZHlTdGF0ZSIsImFkZEV2ZW50TGlzdGVuZXIiLCJzZXRUaW1lb3V0IiwibmV4dFNlcnZlckRhdGFMb2FkaW5nR2xvYmFsIiwic2VsZiIsIl9fbmV4dF9mIiwicmVhZGFibGUiLCJSZWFkYWJsZVN0cmVhbSIsInN0YXJ0IiwiY29udHJvbGxlciIsImluaXRpYWxTZXJ2ZXJSZXNwb25zZSIsImNyZWF0ZUZyb21SZWFkYWJsZVN0cmVhbSIsImNhbGxTZXJ2ZXIiLCJmaW5kU291cmNlTWFwVVJMIiwiU2VydmVyUm9vdCIsInBlbmRpbmdBY3Rpb25RdWV1ZSIsImluaXRpYWxSU0NQYXlsb2FkIiwidXNlIiwiYWN0aW9uUXVldWUiLCJyb3V0ZXIiLCJBcHBSb3V0ZXIiLCJnbG9iYWxFcnJvckNvbXBvbmVudEFuZFN0eWxlcyIsIkciLCJhc3NldFByZWZpeCIsInAiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJtIiwiTWlzc2luZ1Nsb3RDb250ZXh0IiwidmFsdWUiLCJTdHJpY3RNb2RlSWZFbmFibGVkIiwiX19ORVhUX1NUUklDVF9NT0RFX0FQUCIsIlJlYWN0IiwiU3RyaWN0TW9kZSIsIkZyYWdtZW50IiwiUm9vdCIsImNoaWxkcmVuIiwiX19ORVhUX1RFU1RfTU9ERSIsInVzZUVmZmVjdCIsIndpbmRvdyIsIl9fTkVYVF9IWURSQVRFRCIsIl9fTkVYVF9IWURSQVRFRF9BVCIsInBlcmZvcm1hbmNlIiwibm93IiwiX19ORVhUX0hZRFJBVEVEX0NCIiwicmVhY3RSb290T3B0aW9ucyIsIm9uUmVjb3ZlcmFibGVFcnJvciIsIm9uQ2F1Z2h0RXJyb3IiLCJvblVuY2F1Z2h0RXJyb3IiLCJpbnN0cnVtZW50YXRpb25Ib29rcyIsIlByb21pc2UiLCJyZXNvbHZlIiwicmVqZWN0IiwidGhlbiIsInNldEFwcEJ1aWxkSWQiLCJiIiwiaW5pdGlhbFRpbWVzdGFtcCIsIkRhdGUiLCJjcmVhdGVNdXRhYmxlQWN0aW9uUXVldWUiLCJjcmVhdGVJbml0aWFsUm91dGVyU3RhdGUiLCJuYXZpZ2F0ZWRBdCIsImluaXRpYWxGbGlnaHREYXRhIiwiZiIsImluaXRpYWxDYW5vbmljYWxVcmxQYXJ0cyIsImMiLCJpbml0aWFsUGFyYWxsZWxSb3V0ZXMiLCJNYXAiLCJsb2NhdGlvbiIsImNvdWxkQmVJbnRlcmNlcHRlZCIsInBvc3Rwb25lZCIsInMiLCJwcmVyZW5kZXJlZCIsIlMiLCJlcnIiLCJyZWFjdEVsIiwiSGVhZE1hbmFnZXJDb250ZXh0IiwiUHJvdmlkZXIiLCJhcHBEaXIiLCJkb2N1bWVudEVsZW1lbnQiLCJpZCIsImVsZW1lbnQiLCJjcmVhdGVSb290TGV2ZWxEZXZPdmVybGF5RWxlbWVudCIsInJlcXVpcmUiLCJSZWFjdERPTUNsaWVudCIsImNyZWF0ZVJvb3QiLCJyZW5kZXIiLCJzdGFydFRyYW5zaXRpb24iLCJoeWRyYXRlUm9vdCIsImZvcm1TdGF0ZSIsImxpbmtHYyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router-instance.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createMutableActionQueue: function() {\n        return createMutableActionQueue;\n    },\n    dispatchNavigateAction: function() {\n        return dispatchNavigateAction;\n    },\n    dispatchTraverseAction: function() {\n        return dispatchTraverseAction;\n    },\n    getCurrentAppRouterState: function() {\n        return getCurrentAppRouterState;\n    },\n    publicAppRouterInstance: function() {\n        return publicAppRouterInstance;\n    }\n});\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _routerreducer = __webpack_require__(/*! ./router-reducer/router-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _isthenable = __webpack_require__(/*! ../../shared/lib/is-thenable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/is-thenable.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./router-reducer/reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nfunction runRemainingActions(actionQueue, setState) {\n    if (actionQueue.pending !== null) {\n        actionQueue.pending = actionQueue.pending.next;\n        if (actionQueue.pending !== null) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            runAction({\n                actionQueue,\n                action: actionQueue.pending,\n                setState\n            });\n        } else {\n            // No more actions are pending, check if a refresh is needed\n            if (actionQueue.needsRefresh) {\n                actionQueue.needsRefresh = false;\n                actionQueue.dispatch({\n                    type: _routerreducertypes.ACTION_REFRESH,\n                    origin: window.location.origin\n                }, setState);\n            }\n        }\n    }\n}\nasync function runAction(param) {\n    let { actionQueue, action, setState } = param;\n    const prevState = actionQueue.state;\n    actionQueue.pending = action;\n    const payload = action.payload;\n    const actionResult = actionQueue.action(prevState, payload);\n    function handleResult(nextState) {\n        // if we discarded this action, the state should also be discarded\n        if (action.discarded) {\n            return;\n        }\n        actionQueue.state = nextState;\n        runRemainingActions(actionQueue, setState);\n        action.resolve(nextState);\n    }\n    // if the action is a promise, set up a callback to resolve it\n    if ((0, _isthenable.isThenable)(actionResult)) {\n        actionResult.then(handleResult, (err)=>{\n            runRemainingActions(actionQueue, setState);\n            action.reject(err);\n        });\n    } else {\n        handleResult(actionResult);\n    }\n}\nfunction dispatchAction(actionQueue, payload, setState) {\n    let resolvers = {\n        resolve: setState,\n        reject: ()=>{}\n    };\n    // most of the action types are async with the exception of restore\n    // it's important that restore is handled quickly since it's fired on the popstate event\n    // and we don't want to add any delay on a back/forward nav\n    // this only creates a promise for the async actions\n    if (payload.type !== _routerreducertypes.ACTION_RESTORE) {\n        // Create the promise and assign the resolvers to the object.\n        const deferredPromise = new Promise((resolve, reject)=>{\n            resolvers = {\n                resolve,\n                reject\n            };\n        });\n        (0, _react.startTransition)(()=>{\n            // we immediately notify React of the pending promise -- the resolver is attached to the action node\n            // and will be called when the associated action promise resolves\n            setState(deferredPromise);\n        });\n    }\n    const newAction = {\n        payload,\n        next: null,\n        resolve: resolvers.resolve,\n        reject: resolvers.reject\n    };\n    // Check if the queue is empty\n    if (actionQueue.pending === null) {\n        // The queue is empty, so add the action and start it immediately\n        // Mark this action as the last in the queue\n        actionQueue.last = newAction;\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else if (payload.type === _routerreducertypes.ACTION_NAVIGATE || payload.type === _routerreducertypes.ACTION_RESTORE) {\n        // Navigations (including back/forward) take priority over any pending actions.\n        // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n        actionQueue.pending.discarded = true;\n        // The rest of the current queue should still execute after this navigation.\n        // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n        newAction.next = actionQueue.pending.next;\n        // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n        if (actionQueue.pending.payload.type === _routerreducertypes.ACTION_SERVER_ACTION) {\n            actionQueue.needsRefresh = true;\n        }\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else {\n        // The queue is not empty, so add the action to the end of the queue\n        // It will be started by runRemainingActions after the previous action finishes\n        if (actionQueue.last !== null) {\n            actionQueue.last.next = newAction;\n        }\n        actionQueue.last = newAction;\n    }\n}\nlet globalActionQueue = null;\nfunction createMutableActionQueue(initialState, instrumentationHooks) {\n    const actionQueue = {\n        state: initialState,\n        dispatch: (payload, setState)=>dispatchAction(actionQueue, payload, setState),\n        action: async (state, action)=>{\n            const result = (0, _routerreducer.reducer)(state, action);\n            return result;\n        },\n        pending: null,\n        last: null,\n        onRouterTransitionStart: instrumentationHooks !== null && typeof instrumentationHooks.onRouterTransitionStart === 'function' ? instrumentationHooks.onRouterTransitionStart : null\n    };\n    if (true) {\n        // The action queue is lazily created on hydration, but after that point\n        // it doesn't change. So we can store it in a global rather than pass\n        // it around everywhere via props/context.\n        if (globalActionQueue !== null) {\n            throw Object.defineProperty(new Error('Internal Next.js Error: createMutableActionQueue was called more ' + 'than once'), \"__NEXT_ERROR_CODE\", {\n                value: \"E624\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        globalActionQueue = actionQueue;\n    }\n    return actionQueue;\n}\nfunction getCurrentAppRouterState() {\n    return globalActionQueue !== null ? globalActionQueue.state : null;\n}\nfunction getAppRouterActionQueue() {\n    if (globalActionQueue === null) {\n        throw Object.defineProperty(new Error('Internal Next.js error: Router action dispatched before initialization.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E668\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return globalActionQueue;\n}\nfunction getProfilingHookForOnNavigationStart() {\n    if (globalActionQueue !== null) {\n        return globalActionQueue.onRouterTransitionStart;\n    }\n    return null;\n}\nfunction dispatchNavigateAction(href, navigateType, shouldScroll, linkInstanceRef) {\n    // TODO: This stuff could just go into the reducer. Leaving as-is for now\n    // since we're about to rewrite all the router reducer stuff anyway.\n    const url = new URL((0, _addbasepath.addBasePath)(href), location.href);\n    if (false) {}\n    (0, _links.setLinkForCurrentNavigation)(linkInstanceRef);\n    const onRouterTransitionStart = getProfilingHookForOnNavigationStart();\n    if (onRouterTransitionStart !== null) {\n        onRouterTransitionStart(href, navigateType);\n    }\n    (0, _useactionqueue.dispatchAppRouterAction)({\n        type: _routerreducertypes.ACTION_NAVIGATE,\n        url,\n        isExternalUrl: (0, _approuter.isExternalURL)(url),\n        locationSearch: location.search,\n        shouldScroll,\n        navigateType,\n        allowAliasing: true\n    });\n}\nfunction dispatchTraverseAction(href, tree) {\n    const onRouterTransitionStart = getProfilingHookForOnNavigationStart();\n    if (onRouterTransitionStart !== null) {\n        onRouterTransitionStart(href, 'traverse');\n    }\n    (0, _useactionqueue.dispatchAppRouterAction)({\n        type: _routerreducertypes.ACTION_RESTORE,\n        url: new URL(href),\n        tree\n    });\n}\nconst publicAppRouterInstance = {\n    back: ()=>window.history.back(),\n    forward: ()=>window.history.forward(),\n    prefetch:  false ? // cache. So we don't need to dispatch an action.\n    0 : (href, options)=>{\n        // Use the old prefetch implementation.\n        const actionQueue = getAppRouterActionQueue();\n        const url = (0, _approuter.createPrefetchURL)(href);\n        if (url !== null) {\n            var _options_kind;\n            // The prefetch reducer doesn't actually update any state or\n            // trigger a rerender. It just writes to a mutable cache. So we\n            // shouldn't bother calling setState/dispatch; we can just re-run\n            // the reducer directly using the current state.\n            // TODO: Refactor this away from a \"reducer\" so it's\n            // less confusing.\n            (0, _prefetchreducer.prefetchReducer)(actionQueue.state, {\n                type: _routerreducertypes.ACTION_PREFETCH,\n                url,\n                kind: (_options_kind = options == null ? void 0 : options.kind) != null ? _options_kind : _routerreducertypes.PrefetchKind.FULL\n            });\n        }\n    },\n    replace: (href, options)=>{\n        (0, _react.startTransition)(()=>{\n            var _options_scroll;\n            dispatchNavigateAction(href, 'replace', (_options_scroll = options == null ? void 0 : options.scroll) != null ? _options_scroll : true, null);\n        });\n    },\n    push: (href, options)=>{\n        (0, _react.startTransition)(()=>{\n            var _options_scroll;\n            dispatchNavigateAction(href, 'push', (_options_scroll = options == null ? void 0 : options.scroll) != null ? _options_scroll : true, null);\n        });\n    },\n    refresh: ()=>{\n        (0, _react.startTransition)(()=>{\n            (0, _useactionqueue.dispatchAppRouterAction)({\n                type: _routerreducertypes.ACTION_REFRESH,\n                origin: window.location.origin\n            });\n        });\n    },\n    hmrRefresh: ()=>{\n        if (false) {} else {\n            (0, _react.startTransition)(()=>{\n                (0, _useactionqueue.dispatchAppRouterAction)({\n                    type: _routerreducertypes.ACTION_HMR_REFRESH,\n                    origin: window.location.origin\n                });\n            });\n        }\n    }\n};\n// Exists for debugging purposes. Don't use in application code.\nif ( true && window.next) {\n    window.next.router = publicAppRouterInstance;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-instance.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createEmptyCacheNode: function() {\n        return createEmptyCacheNode;\n    },\n    createPrefetchURL: function() {\n        return createPrefetchURL;\n    },\n    default: function() {\n        return AppRouter;\n    },\n    isExternalURL: function() {\n        return isExternalURL;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _createhreffromurl = __webpack_require__(/*! ./router-reducer/create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\nconst _errorboundary = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\"));\nconst _isbot = __webpack_require__(/*! ../../shared/lib/router/utils/is-bot */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuterannouncer = __webpack_require__(/*! ./app-router-announcer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _findheadincache = __webpack_require__(/*! ./router-reducer/reducers/find-head-in-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _removebasepath = __webpack_require__(/*! ../remove-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _computechangedpath = __webpack_require__(/*! ./router-reducer/compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _navfailurehandler = __webpack_require__(/*! ./nav-failure-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/nav-failure-handler.js\");\nconst _approuterinstance = __webpack_require__(/*! ./app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _redirect = __webpack_require__(/*! ./redirect */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect.js\");\nconst _redirecterror = __webpack_require__(/*! ./redirect-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-error.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst globalMutable = {};\nfunction isExternalURL(url) {\n    return url.origin !== window.location.origin;\n}\nfunction createPrefetchURL(href) {\n    // Don't prefetch for bots as they don't navigate.\n    if ((0, _isbot.isBot)(window.navigator.userAgent)) {\n        return null;\n    }\n    let url;\n    try {\n        url = new URL((0, _addbasepath.addBasePath)(href), window.location.href);\n    } catch (_) {\n        // TODO: Does this need to throw or can we just console.error instead? Does\n        // anyone rely on this throwing? (Seems unlikely.)\n        throw Object.defineProperty(new Error(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E234\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // Don't prefetch during development (improves compilation performance)\n    if (true) {\n        return null;\n    }\n    // External urls can't be prefetched in the same way.\n    if (isExternalURL(url)) {\n        return null;\n    }\n    return url;\n}\nfunction HistoryUpdater(param) {\n    let { appRouterState } = param;\n    (0, _react.useInsertionEffect)(()=>{\n        if (false) {}\n        const { tree, pushRef, canonicalUrl } = appRouterState;\n        const historyState = {\n            ...pushRef.preserveCustomHistoryState ? window.history.state : {},\n            // Identifier is shortened intentionally.\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            // __N is used to identify if the history entry can be handled by the old router.\n            __NA: true,\n            __PRIVATE_NEXTJS_INTERNALS_TREE: tree\n        };\n        if (pushRef.pendingPush && // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n        // This mirrors the browser behavior for normal navigation.\n        (0, _createhreffromurl.createHrefFromUrl)(new URL(window.location.href)) !== canonicalUrl) {\n            // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n            pushRef.pendingPush = false;\n            window.history.pushState(historyState, '', canonicalUrl);\n        } else {\n            window.history.replaceState(historyState, '', canonicalUrl);\n        }\n    }, [\n        appRouterState\n    ]);\n    (0, _react.useEffect)(()=>{\n        // The Next-Url and the base tree may affect the result of a prefetch\n        // task. Re-prefetch all visible links with the updated values. In most\n        // cases, this will not result in any new network requests, only if\n        // the prefetch result actually varies on one of these inputs.\n        if (false) {}\n    }, [\n        appRouterState.nextUrl,\n        appRouterState.tree\n    ]);\n    return null;\n}\n_c = HistoryUpdater;\nfunction createEmptyCacheNode() {\n    return {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1\n    };\n}\nfunction copyNextJsInternalHistoryState(data) {\n    if (data == null) data = {};\n    const currentState = window.history.state;\n    const __NA = currentState == null ? void 0 : currentState.__NA;\n    if (__NA) {\n        data.__NA = __NA;\n    }\n    const __PRIVATE_NEXTJS_INTERNALS_TREE = currentState == null ? void 0 : currentState.__PRIVATE_NEXTJS_INTERNALS_TREE;\n    if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n        data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE;\n    }\n    return data;\n}\nfunction Head(param) {\n    let { headCacheNode } = param;\n    // If this segment has a `prefetchHead`, it's the statically prefetched data.\n    // We should use that on initial render instead of `head`. Then we'll switch\n    // to `head` when the dynamic response streams in.\n    const head = headCacheNode !== null ? headCacheNode.head : null;\n    const prefetchHead = headCacheNode !== null ? headCacheNode.prefetchHead : null;\n    // If no prefetch data is available, then we go straight to rendering `head`.\n    const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    return (0, _react.useDeferredValue)(head, resolvedPrefetchRsc);\n}\n_c1 = Head;\n/**\n * The global router that wraps the application components.\n */ function Router(param) {\n    let { actionQueue, assetPrefix, globalError } = param;\n    const state = (0, _useactionqueue.useActionQueue)(actionQueue);\n    const { canonicalUrl } = state;\n    // Add memoized pathname/query for useSearchParams and usePathname.\n    const { searchParams, pathname } = (0, _react.useMemo)(()=>{\n        const url = new URL(canonicalUrl,  false ? 0 : window.location.href);\n        return {\n            // This is turned into a readonly class in `useSearchParams`\n            searchParams: url.searchParams,\n            pathname: (0, _hasbasepath.hasBasePath)(url.pathname) ? (0, _removebasepath.removeBasePath)(url.pathname) : url.pathname\n        };\n    }, [\n        canonicalUrl\n    ]);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const { cache, prefetchCache, tree } = state;\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            // Add `window.nd` for debugging purposes.\n            // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n            // @ts-ignore this is for debugging\n            window.nd = {\n                router: _approuterinstance.publicAppRouterInstance,\n                cache,\n                prefetchCache,\n                tree\n            };\n        }, [\n            cache,\n            prefetchCache,\n            tree\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        // If the app is restored from bfcache, it's possible that\n        // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n        // would trigger the mpa navigation logic again from the lines below.\n        // This will restore the router to the initial state in the event that the app is restored from bfcache.\n        function handlePageShow(event) {\n            var _window_history_state;\n            if (!event.persisted || !((_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE)) {\n                return;\n            }\n            // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n            // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n            // of the last MPA navigation.\n            globalMutable.pendingMpaPath = undefined;\n            (0, _useactionqueue.dispatchAppRouterAction)({\n                type: _routerreducertypes.ACTION_RESTORE,\n                url: new URL(window.location.href),\n                tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n            });\n        }\n        window.addEventListener('pageshow', handlePageShow);\n        return ()=>{\n            window.removeEventListener('pageshow', handlePageShow);\n        };\n    }, []);\n    (0, _react.useEffect)(()=>{\n        // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n        // are caught and handled by the router.\n        function handleUnhandledRedirect(event) {\n            const error = 'reason' in event ? event.reason : event.error;\n            if ((0, _redirecterror.isRedirectError)(error)) {\n                event.preventDefault();\n                const url = (0, _redirect.getURLFromRedirectError)(error);\n                const redirectType = (0, _redirect.getRedirectTypeFromError)(error);\n                // TODO: This should access the router methods directly, rather than\n                // go through the public interface.\n                if (redirectType === _redirecterror.RedirectType.push) {\n                    _approuterinstance.publicAppRouterInstance.push(url, {});\n                } else {\n                    _approuterinstance.publicAppRouterInstance.replace(url, {});\n                }\n            }\n        }\n        window.addEventListener('error', handleUnhandledRedirect);\n        window.addEventListener('unhandledrejection', handleUnhandledRedirect);\n        return ()=>{\n            window.removeEventListener('error', handleUnhandledRedirect);\n            window.removeEventListener('unhandledrejection', handleUnhandledRedirect);\n        };\n    }, []);\n    // When mpaNavigation flag is set do a hard navigation to the new url.\n    // Infinitely suspend because we don't actually want to rerender any child\n    // components with the new URL and any entangled state updates shouldn't\n    // commit either (eg: useTransition isPending should stay true until the page\n    // unloads).\n    //\n    // This is a side effect in render. Don't try this at home, kids. It's\n    // probably safe because we know this is a singleton component and it's never\n    // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n    // but that's... fine?)\n    const { pushRef } = state;\n    if (pushRef.mpaNavigation) {\n        // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n        if (globalMutable.pendingMpaPath !== canonicalUrl) {\n            const location = window.location;\n            if (pushRef.pendingPush) {\n                location.assign(canonicalUrl);\n            } else {\n                location.replace(canonicalUrl);\n            }\n            globalMutable.pendingMpaPath = canonicalUrl;\n        }\n        // TODO-APP: Should we listen to navigateerror here to catch failed\n        // navigations somehow? And should we call window.stop() if a SPA navigation\n        // should interrupt an MPA one?\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    (0, _react.useEffect)(()=>{\n        const originalPushState = window.history.pushState.bind(window.history);\n        const originalReplaceState = window.history.replaceState.bind(window.history);\n        // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n        const applyUrlFromHistoryPushReplace = (url)=>{\n            var _window_history_state;\n            const href = window.location.href;\n            const tree = (_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE;\n            (0, _react.startTransition)(()=>{\n                (0, _useactionqueue.dispatchAppRouterAction)({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(url != null ? url : href, href),\n                    tree\n                });\n            });\n        };\n        /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.pushState = function pushState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalPushState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalPushState(data, _unused, url);\n        };\n        /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.replaceState = function replaceState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalReplaceState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalReplaceState(data, _unused, url);\n        };\n        /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */ const onPopState = (event)=>{\n            if (!event.state) {\n                // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n                return;\n            }\n            // This case happens when the history entry was pushed by the `pages` router.\n            if (!event.state.__NA) {\n                window.location.reload();\n                return;\n            }\n            // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n            // Without startTransition works if the cache is there for this path\n            (0, _react.startTransition)(()=>{\n                (0, _approuterinstance.dispatchTraverseAction)(window.location.href, event.state.__PRIVATE_NEXTJS_INTERNALS_TREE);\n            });\n        };\n        // Register popstate event to call onPopstate.\n        window.addEventListener('popstate', onPopState);\n        return ()=>{\n            window.history.pushState = originalPushState;\n            window.history.replaceState = originalReplaceState;\n            window.removeEventListener('popstate', onPopState);\n        };\n    }, []);\n    const { cache, tree, nextUrl, focusAndScrollRef } = state;\n    const matchingHead = (0, _react.useMemo)(()=>{\n        return (0, _findheadincache.findHeadInCache)(cache, tree[1]);\n    }, [\n        cache,\n        tree\n    ]);\n    // Add memoized pathParams for useParams.\n    const pathParams = (0, _react.useMemo)(()=>{\n        return (0, _computechangedpath.getSelectedParams)(tree);\n    }, [\n        tree\n    ]);\n    const layoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            parentTree: tree,\n            parentCacheNode: cache,\n            parentSegmentPath: null,\n            // Root node always has `url`\n            // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n            url: canonicalUrl\n        };\n    }, [\n        tree,\n        cache,\n        canonicalUrl\n    ]);\n    const globalLayoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            tree,\n            focusAndScrollRef,\n            nextUrl\n        };\n    }, [\n        tree,\n        focusAndScrollRef,\n        nextUrl\n    ]);\n    let head;\n    if (matchingHead !== null) {\n        // The head is wrapped in an extra component so we can use\n        // `useDeferredValue` to swap between the prefetched and final versions of\n        // the head. (This is what LayoutRouter does for segment data, too.)\n        //\n        // The `key` is used to remount the component whenever the head moves to\n        // a different segment.\n        const [headCacheNode, headKey] = matchingHead;\n        head = /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n            headCacheNode: headCacheNode\n        }, headKey);\n    } else {\n        head = null;\n    }\n    let content = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_redirectboundary.RedirectBoundary, {\n        children: [\n            head,\n            cache.rsc,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuterannouncer.AppRouterAnnouncer, {\n                tree: tree\n            })\n        ]\n    });\n    if (true) {\n        // In development, we apply few error boundaries and hot-reloader:\n        // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n        // - HotReloader:\n        //  - hot-reload the app when the code changes\n        //  - render dev overlay\n        //  - catch runtime errors and display global-error when necessary\n        if (true) {\n            const { DevRootHTTPAccessFallbackBoundary } = __webpack_require__(/*! ./dev-root-http-access-fallback-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js\");\n            content = /*#__PURE__*/ (0, _jsxruntime.jsx)(DevRootHTTPAccessFallbackBoundary, {\n                children: content\n            });\n        }\n        const HotReloader = (__webpack_require__(/*! ./react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\")[\"default\"]);\n        content = /*#__PURE__*/ (0, _jsxruntime.jsx)(HotReloader, {\n            assetPrefix: assetPrefix,\n            globalError: globalError,\n            children: content\n        });\n    } else {}\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(HistoryUpdater, {\n                appRouterState: state\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(RuntimeStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                value: pathParams,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathnameContext.Provider, {\n                    value: pathname,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                        value: searchParams,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.GlobalLayoutRouterContext.Provider, {\n                            value: globalLayoutRouterContext,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n                                value: _approuterinstance.publicAppRouterInstance,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n                                    value: layoutRouterContext,\n                                    children: content\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        ]\n    });\n}\n_c2 = Router;\nfunction AppRouter(param) {\n    let { actionQueue, globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles], assetPrefix } = param;\n    (0, _navfailurehandler.useNavFailureHandler)();\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n        // At the very top level, use the default GlobalError component as the final fallback.\n        // When the app router itself fails, which means the framework itself fails, we show the default error.\n        errorComponent: _errorboundary.default,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Router, {\n            actionQueue: actionQueue,\n            assetPrefix: assetPrefix,\n            globalError: [\n                globalErrorComponent,\n                globalErrorStyles\n            ]\n        })\n    });\n}\n_c3 = AppRouter;\nconst runtimeStyles = new Set();\nlet runtimeStyleChanged = new Set();\nglobalThis._N_E_STYLE_LOAD = function(href) {\n    let len = runtimeStyles.size;\n    runtimeStyles.add(href);\n    if (runtimeStyles.size !== len) {\n        runtimeStyleChanged.forEach((cb)=>cb());\n    }\n    // TODO figure out how to get a promise here\n    // But maybe it's not necessary as react would block rendering until it's loaded\n    return Promise.resolve();\n};\nfunction RuntimeStyles() {\n    _s();\n    const [, forceUpdate] = _react.default.useState(0);\n    const renderedStylesSize = runtimeStyles.size;\n    (0, _react.useEffect)(()=>{\n        const changed = ()=>forceUpdate((c)=>c + 1);\n        runtimeStyleChanged.add(changed);\n        if (renderedStylesSize !== runtimeStyles.size) {\n            changed();\n        }\n        return ()=>{\n            runtimeStyleChanged.delete(changed);\n        };\n    }, [\n        renderedStylesSize,\n        forceUpdate\n    ]);\n    const dplId =  false ? 0 : '';\n    return [\n        ...runtimeStyles\n    ].map((href, i)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            rel: \"stylesheet\",\n            href: \"\" + href + dplId,\n            // @ts-ignore\n            precedence: \"next\"\n        }, i));\n}\n_s(RuntimeStyles, \"Eht7Kgdrrgt5B4LSklQ7qDPo8Aw=\");\n_c4 = RuntimeStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HistoryUpdater\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Router\");\n$RefreshReg$(_c3, \"AppRouter\");\n$RefreshReg$(_c4, \"RuntimeStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/globals/intercept-console-error.js ***!
  \*************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    originConsoleError: function() {\n        return originConsoleError;\n    },\n    patchConsoleError: function() {\n        return patchConsoleError;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _isnextroutererror = __webpack_require__(/*! ../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _console = __webpack_require__(/*! ../../lib/console */ \"(app-pages-browser)/./node_modules/next/dist/client/lib/console.js\");\nconst originConsoleError = globalThis.console.error;\nfunction patchConsoleError() {\n    // Ensure it's only patched once\n    if (false) {}\n    window.console.error = function error() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        let maybeError;\n        if (true) {\n            const { error: replayedError } = (0, _console.parseConsoleArgs)(args);\n            if (replayedError) {\n                maybeError = replayedError;\n            } else if ((0, _iserror.default)(args[0])) {\n                maybeError = args[0];\n            } else {\n                // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n                maybeError = args[1];\n            }\n        } else {}\n        if (!(0, _isnextroutererror.isNextRouterError)(maybeError)) {\n            if (true) {\n                (0, _useerrorhandler.handleConsoleError)(// but if we pass the error directly, `handleClientError` will ignore it\n                maybeError, args);\n            }\n            originConsoleError.apply(window.console, args);\n        }\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=intercept-console-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HTTPAccessFallbackBoundary\", ({\n    enumerable: true,\n    get: function() {\n        return HTTPAccessFallbackBoundary;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _navigationuntracked = __webpack_require__(/*! ../navigation-untracked */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation-untracked.js\");\nconst _httpaccessfallback = __webpack_require__(/*! ./http-access-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\");\nconst _warnonce = __webpack_require__(/*! ../../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nclass HTTPAccessFallbackErrorBoundary extends _react.default.Component {\n    componentDidCatch() {\n        if ( true && this.props.missingSlots && this.props.missingSlots.size > 0 && // A missing children slot is the typical not-found case, so no need to warn\n        !this.props.missingSlots.has('children')) {\n            let warningMessage = 'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n';\n            const formattedSlots = Array.from(this.props.missingSlots).sort((a, b)=>a.localeCompare(b)).map((slot)=>\"@\" + slot).join(', ');\n            warningMessage += 'Missing slots: ' + formattedSlots;\n            (0, _warnonce.warnOnce)(warningMessage);\n        }\n    }\n    static getDerivedStateFromError(error) {\n        if ((0, _httpaccessfallback.isHTTPAccessFallbackError)(error)) {\n            const httpStatus = (0, _httpaccessfallback.getAccessFallbackHTTPStatus)(error);\n            return {\n                triggeredStatus: httpStatus\n            };\n        }\n        // Re-throw if error is not for 404\n        throw error;\n    }\n    static getDerivedStateFromProps(props, state) {\n        /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */ if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n            return {\n                triggeredStatus: undefined,\n                previousPathname: props.pathname\n            };\n        }\n        return {\n            triggeredStatus: state.triggeredStatus,\n            previousPathname: props.pathname\n        };\n    }\n    render() {\n        const { notFound, forbidden, unauthorized, children } = this.props;\n        const { triggeredStatus } = this.state;\n        const errorComponents = {\n            [_httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n            [_httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n            [_httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized\n        };\n        if (triggeredStatus) {\n            const isNotFound = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND && notFound;\n            const isForbidden = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN && forbidden;\n            const isUnauthorized = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized;\n            // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n            if (!(isNotFound || isForbidden || isUnauthorized)) {\n                return children;\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex\"\n                    }),\n                     true && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"boundary-next-error\",\n                        content: (0, _httpaccessfallback.getAccessFallbackErrorTypeByStatus)(triggeredStatus)\n                    }),\n                    errorComponents[triggeredStatus]\n                ]\n            });\n        }\n        return children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            triggeredStatus: undefined,\n            previousPathname: props.pathname\n        };\n    }\n}\nfunction HTTPAccessFallbackBoundary(param) {\n    let { notFound, forbidden, unauthorized, children } = param;\n    // When we're rendering the missing params shell, this will return null. This\n    // is because we won't be rendering any not found boundaries or error\n    // boundaries for the missing params shell. When this runs on the client\n    // (where these error can occur), we will get the correct pathname.\n    const pathname = (0, _navigationuntracked.useUntrackedPathname)();\n    const missingSlots = (0, _react.useContext)(_approutercontextsharedruntime.MissingSlotContext);\n    const hasErrorFallback = !!(notFound || forbidden || unauthorized);\n    if (hasErrorFallback) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(HTTPAccessFallbackErrorBoundary, {\n            pathname: pathname,\n            notFound: notFound,\n            forbidden: forbidden,\n            unauthorized: unauthorized,\n            missingSlots: missingSlots,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c = HTTPAccessFallbackBoundary;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary.js.map\nvar _c;\n$RefreshReg$(_c, \"HTTPAccessFallbackBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/links.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/components/links.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    IDLE_LINK_STATUS: function() {\n        return IDLE_LINK_STATUS;\n    },\n    PENDING_LINK_STATUS: function() {\n        return PENDING_LINK_STATUS;\n    },\n    mountFormInstance: function() {\n        return mountFormInstance;\n    },\n    mountLinkInstance: function() {\n        return mountLinkInstance;\n    },\n    onLinkVisibilityChanged: function() {\n        return onLinkVisibilityChanged;\n    },\n    onNavigationIntent: function() {\n        return onNavigationIntent;\n    },\n    pingVisibleLinks: function() {\n        return pingVisibleLinks;\n    },\n    setLinkForCurrentNavigation: function() {\n        return setLinkForCurrentNavigation;\n    },\n    unmountLinkForCurrentNavigation: function() {\n        return unmountLinkForCurrentNavigation;\n    },\n    unmountPrefetchableInstance: function() {\n        return unmountPrefetchableInstance;\n    }\n});\nconst _approuterinstance = __webpack_require__(/*! ./app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n// Tracks the most recently navigated link instance. When null, indicates\n// the current navigation was not initiated by a link click.\nlet linkForMostRecentNavigation = null;\nconst PENDING_LINK_STATUS = {\n    pending: true\n};\nconst IDLE_LINK_STATUS = {\n    pending: false\n};\nfunction setLinkForCurrentNavigation(link) {\n    (0, _react.startTransition)(()=>{\n        linkForMostRecentNavigation == null ? void 0 : linkForMostRecentNavigation.setOptimisticLinkStatus(IDLE_LINK_STATUS);\n        link == null ? void 0 : link.setOptimisticLinkStatus(PENDING_LINK_STATUS);\n        linkForMostRecentNavigation = link;\n    });\n}\nfunction unmountLinkForCurrentNavigation(link) {\n    if (linkForMostRecentNavigation === link) {\n        linkForMostRecentNavigation = null;\n    }\n}\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst prefetchable = typeof WeakMap === 'function' ? new WeakMap() : new Map();\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst prefetchableAndVisible = new Set();\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer = typeof IntersectionObserver === 'function' ? new IntersectionObserver(handleIntersect, {\n    rootMargin: '200px'\n}) : null;\nfunction observeVisibility(element, instance) {\n    const existingInstance = prefetchable.get(element);\n    if (existingInstance !== undefined) {\n        // This shouldn't happen because each <Link> component should have its own\n        // anchor tag instance, but it's defensive coding to avoid a memory leak in\n        // case there's a logical error somewhere else.\n        unmountPrefetchableInstance(element);\n    }\n    // Only track prefetchable links that have a valid prefetch URL\n    prefetchable.set(element, instance);\n    if (observer !== null) {\n        observer.observe(element);\n    }\n}\nfunction coercePrefetchableUrl(href) {\n    try {\n        return (0, _approuter.createPrefetchURL)(href);\n    } catch (e) {\n        // createPrefetchURL sometimes throws an error if an invalid URL is\n        // provided, though I'm not sure if it's actually necessary.\n        // TODO: Consider removing the throw from the inner function, or change it\n        // to reportError. Or maybe the error isn't even necessary for automatic\n        // prefetches, just navigations.\n        const reportErrorFn = typeof reportError === 'function' ? reportError : console.error;\n        reportErrorFn(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\");\n        return null;\n    }\n}\nfunction mountLinkInstance(element, href, router, kind, prefetchEnabled, setOptimisticLinkStatus) {\n    if (prefetchEnabled) {\n        const prefetchURL = coercePrefetchableUrl(href);\n        if (prefetchURL !== null) {\n            const instance = {\n                router,\n                kind,\n                isVisible: false,\n                wasHoveredOrTouched: false,\n                prefetchTask: null,\n                cacheVersion: -1,\n                prefetchHref: prefetchURL.href,\n                setOptimisticLinkStatus\n            };\n            // We only observe the link's visibility if it's prefetchable. For\n            // example, this excludes links to external URLs.\n            observeVisibility(element, instance);\n            return instance;\n        }\n    }\n    // If the link is not prefetchable, we still create an instance so we can\n    // track its optimistic state (i.e. useLinkStatus).\n    const instance = {\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1,\n        prefetchHref: null,\n        setOptimisticLinkStatus\n    };\n    return instance;\n}\nfunction mountFormInstance(element, href, router, kind) {\n    const prefetchURL = coercePrefetchableUrl(href);\n    if (prefetchURL === null) {\n        // This href is not prefetchable, so we don't track it.\n        // TODO: We currently observe/unobserve a form every time its href changes.\n        // For Links, this isn't a big deal because the href doesn't usually change,\n        // but for forms it's extremely common. We should optimize this.\n        return;\n    }\n    const instance = {\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1,\n        prefetchHref: prefetchURL.href,\n        setOptimisticLinkStatus: null\n    };\n    observeVisibility(element, instance);\n}\nfunction unmountPrefetchableInstance(element) {\n    const instance = prefetchable.get(element);\n    if (instance !== undefined) {\n        prefetchable.delete(element);\n        prefetchableAndVisible.delete(instance);\n        const prefetchTask = instance.prefetchTask;\n        if (prefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(prefetchTask);\n        }\n    }\n    if (observer !== null) {\n        observer.unobserve(element);\n    }\n}\nfunction handleIntersect(entries) {\n    for (const entry of entries){\n        // Some extremely old browsers or polyfills don't reliably support\n        // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n        // really. But whatever this is fine.)\n        const isVisible = entry.intersectionRatio > 0;\n        onLinkVisibilityChanged(entry.target, isVisible);\n    }\n}\nfunction onLinkVisibilityChanged(element, isVisible) {\n    if (true) {\n        // Prefetching on viewport is disabled in development for performance\n        // reasons, because it requires compiling the target page.\n        // TODO: Investigate re-enabling this.\n        return;\n    }\n    const instance = prefetchable.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    instance.isVisible = isVisible;\n    if (isVisible) {\n        prefetchableAndVisible.add(instance);\n    } else {\n        prefetchableAndVisible.delete(instance);\n    }\n    rescheduleLinkPrefetch(instance);\n}\nfunction onNavigationIntent(element, unstable_upgradeToDynamicPrefetch) {\n    const instance = prefetchable.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    // Prefetch the link on hover/touchstart.\n    if (instance !== undefined) {\n        instance.wasHoveredOrTouched = true;\n        if (false) {}\n        rescheduleLinkPrefetch(instance);\n    }\n}\nfunction rescheduleLinkPrefetch(instance) {\n    const existingPrefetchTask = instance.prefetchTask;\n    if (!instance.isVisible) {\n        // Cancel any in-progress prefetch task. (If it already finished then this\n        // is a no-op.)\n        if (existingPrefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(existingPrefetchTask);\n        }\n        // We don't need to reset the prefetchTask to null upon cancellation; an\n        // old task object can be rescheduled with reschedulePrefetchTask. This is a\n        // micro-optimization but also makes the code simpler (don't need to\n        // worry about whether an old task object is stale).\n        return;\n    }\n    if (true) {\n        // The old prefetch implementation does not have different priority levels.\n        // Just schedule a new prefetch task.\n        prefetchWithOldCacheImplementation(instance);\n        return;\n    }\n    // In the Segment Cache implementation, we assign a higher priority level to\n    // links that were at one point hovered or touched. Since the queue is last-\n    // in-first-out, the highest priority Link is whichever one was hovered last.\n    //\n    // We also increase the relative priority of links whenever they re-enter the\n    // viewport, as if they were being scheduled for the first time.\n    const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n    const appRouterState = (0, _approuterinstance.getCurrentAppRouterState)();\n    if (appRouterState !== null) {\n        const treeAtTimeOfPrefetch = appRouterState.tree;\n        if (existingPrefetchTask === null) {\n            // Initiate a prefetch task.\n            const nextUrl = appRouterState.nextUrl;\n            const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n            instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        } else {\n            // We already have an old task object that we can reschedule. This is\n            // effectively the same as canceling the old task and creating a new one.\n            (0, _segmentcache.reschedulePrefetchTask)(existingPrefetchTask, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        }\n        // Keep track of the cache version at the time the prefetch was requested.\n        // This is used to check if the prefetch is stale.\n        instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    }\n}\nfunction pingVisibleLinks(nextUrl, tree) {\n    // For each currently visible link, cancel the existing prefetch task (if it\n    // exists) and schedule a new one. This is effectively the same as if all the\n    // visible links left and then re-entered the viewport.\n    //\n    // This is called when the Next-Url or the base tree changes, since those\n    // may affect the result of a prefetch task. It's also called after a\n    // cache invalidation.\n    const currentCacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    for (const instance of prefetchableAndVisible){\n        const task = instance.prefetchTask;\n        if (task !== null && instance.cacheVersion === currentCacheVersion && task.key.nextUrl === nextUrl && task.treeAtTimeOfPrefetch === tree) {\n            continue;\n        }\n        // Something changed. Cancel the existing prefetch task and schedule a\n        // new one.\n        if (task !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(task);\n        }\n        const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n        const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n        instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, tree, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    }\n}\nfunction prefetchWithOldCacheImplementation(instance) {\n    // This is the path used when the Segment Cache is not enabled.\n    if (false) {}\n    const doPrefetch = async ()=>{\n        // note that `appRouter.prefetch()` is currently sync,\n        // so we have to wrap this call in an async function to be able to catch() errors below.\n        return instance.router.prefetch(instance.prefetchHref, {\n            kind: instance.kind\n        });\n    };\n    // Prefetch the page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=links.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/links.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js ***!
  \*******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AppDevOverlay\", ({\n    enumerable: true,\n    get: function() {\n        return AppDevOverlay;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _appdevoverlayerrorboundary = __webpack_require__(/*! ./app-dev-overlay-error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\");\nconst _fontstyles = __webpack_require__(/*! ../font/font-styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js\");\nconst _devoverlay = __webpack_require__(/*! ../ui/dev-overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/dev-overlay.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _constants = __webpack_require__(/*! ../../../../shared/lib/errors/constants */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/errors/constants.js\");\nfunction readSsrError() {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    const ssrErrorTemplateTag = document.querySelector('template[data-next-error-message]');\n    if (ssrErrorTemplateTag) {\n        const message = ssrErrorTemplateTag.getAttribute('data-next-error-message');\n        const stack = ssrErrorTemplateTag.getAttribute('data-next-error-stack');\n        const digest = ssrErrorTemplateTag.getAttribute('data-next-error-digest');\n        const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n        if (digest) {\n            ;\n            error.digest = digest;\n        }\n        // Skip Next.js SSR'd internal errors that which will be handled by the error boundaries.\n        if ((0, _isnextroutererror.isNextRouterError)(error)) {\n            return null;\n        }\n        error.stack = stack || '';\n        return error;\n    }\n    return null;\n}\n// Needs to be in the same error boundary as the shell.\n// If it commits, we know we recovered from an SSR error.\n// If it doesn't commit, we errored again and React will take care of error reporting.\nfunction ReplaySsrOnlyErrors(param) {\n    let { onBlockingError } = param;\n    if (true) {\n        // Need to read during render. The attributes will be gone after commit.\n        const ssrError = readSsrError();\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            if (ssrError !== null) {\n                // TODO(veil): Produces wrong Owner Stack\n                // TODO(veil): Mark as recoverable error\n                // TODO(veil): console.error\n                (0, _useerrorhandler.handleClientError)(ssrError);\n                // If it's missing root tags, we can't recover, make it blocking.\n                if (ssrError.digest === _constants.MISSING_ROOT_TAGS_ERROR) {\n                    onBlockingError();\n                }\n            }\n        }, [\n            ssrError,\n            onBlockingError\n        ]);\n    }\n    return null;\n}\n_c = ReplaySsrOnlyErrors;\nfunction AppDevOverlay(param) {\n    let { state, globalError, children } = param;\n    const [isErrorOverlayOpen, setIsErrorOverlayOpen] = (0, _react.useState)(false);\n    const openOverlay = (0, _react.useCallback)(()=>{\n        setIsErrorOverlayOpen(true);\n    }, []);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_appdevoverlayerrorboundary.AppDevOverlayErrorBoundary, {\n                globalError: globalError,\n                onError: setIsErrorOverlayOpen,\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(ReplaySsrOnlyErrors, {\n                        onBlockingError: openOverlay\n                    }),\n                    children\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_fontstyles.FontStyles, {}),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_devoverlay.DevOverlay, {\n                        state: state,\n                        isErrorOverlayOpen: isErrorOverlayOpen,\n                        setIsErrorOverlayOpen: setIsErrorOverlayOpen\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c1 = AppDevOverlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dev-overlay.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"ReplaySsrOnlyErrors\");\n$RefreshReg$(_c1, \"AppDevOverlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js ***!
  \************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createInitialRouterState\", ({\n    enumerable: true,\n    get: function() {\n        return createInitialRouterState;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ./create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ./fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _computechangedpath = __webpack_require__(/*! ./compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ./prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ./refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nfunction createInitialRouterState(param) {\n    let { navigatedAt, initialFlightData, initialCanonicalUrlParts, initialParallelRoutes, location, couldBeIntercepted, postponed, prerendered } = param;\n    // When initialized on the server, the canonical URL is provided as an array of parts.\n    // This is to ensure that when the RSC payload streamed to the client, crawlers don't interpret it\n    // as a URL that should be crawled.\n    const initialCanonicalUrl = initialCanonicalUrlParts.join('/');\n    const normalizedFlightData = (0, _flightdatahelpers.getFlightDataPartsFromPath)(initialFlightData[0]);\n    const { tree: initialTree, seedData: initialSeedData, head: initialHead } = normalizedFlightData;\n    // For the SSR render, seed data should always be available (we only send back a `null` response\n    // in the case of a `loading` segment, pre-PPR.)\n    const rsc = initialSeedData == null ? void 0 : initialSeedData[1];\n    var _initialSeedData_;\n    const loading = (_initialSeedData_ = initialSeedData == null ? void 0 : initialSeedData[3]) != null ? _initialSeedData_ : null;\n    const cache = {\n        lazyData: null,\n        rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        // The cache gets seeded during the first render. `initialParallelRoutes` ensures the cache from the first render is there during the second render.\n        parallelRoutes: initialParallelRoutes,\n        loading,\n        navigatedAt\n    };\n    const canonicalUrl = // This is safe to do as canonicalUrl can't be rendered, it's only used to control the history updates in the useEffect further down in this file.\n    location ? (0, _createhreffromurl.createHrefFromUrl)(location) : initialCanonicalUrl;\n    (0, _refetchinactiveparallelsegments.addRefreshMarkerToActiveParallelSegments)(initialTree, canonicalUrl);\n    const prefetchCache = new Map();\n    // When the cache hasn't been seeded yet we fill the cache with the head.\n    if (initialParallelRoutes === null || initialParallelRoutes.size === 0) {\n        (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(navigatedAt, cache, undefined, initialTree, initialSeedData, initialHead, undefined);\n    }\n    var _ref;\n    const initialState = {\n        tree: initialTree,\n        cache,\n        prefetchCache,\n        pushRef: {\n            pendingPush: false,\n            mpaNavigation: false,\n            // First render needs to preserve the previous window.history.state\n            // to avoid it being overwritten on navigation back/forward with MPA Navigation.\n            preserveCustomHistoryState: true\n        },\n        focusAndScrollRef: {\n            apply: false,\n            onlyHashChange: false,\n            hashFragment: null,\n            segmentPaths: []\n        },\n        canonicalUrl,\n        nextUrl: (_ref = (0, _computechangedpath.extractPathFromFlightRouterState)(initialTree) || (location == null ? void 0 : location.pathname)) != null ? _ref : null\n    };\n    if (false) {}\n    return initialState;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=create-initial-router-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js ***!
  \******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createFetch: function() {\n        return createFetch;\n    },\n    createFromNextReadableStream: function() {\n        return createFromNextReadableStream;\n    },\n    fetchServerResponse: function() {\n        return fetchServerResponse;\n    },\n    urlToUrlWithoutFlightMarker: function() {\n        return urlToUrlWithoutFlightMarker;\n    }\n});\nconst _approuterheaders = __webpack_require__(/*! ../app-router-headers */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\");\nconst _appcallserver = __webpack_require__(/*! ../../app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ../../app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nconst _appbuildid = __webpack_require__(/*! ../../app-build-id */ \"(app-pages-browser)/./node_modules/next/dist/client/app-build-id.js\");\nconst _setcachebustingsearchparam = __webpack_require__(/*! ./set-cache-busting-search-param */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/set-cache-busting-search-param.js\");\n// @ts-ignore\n// eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromReadableStream } from 'react-server-dom-webpack/client'\nconst { createFromReadableStream } =  false ? 0 : __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nfunction urlToUrlWithoutFlightMarker(url) {\n    const urlWithoutFlightParameters = new URL(url, location.origin);\n    urlWithoutFlightParameters.searchParams.delete(_approuterheaders.NEXT_RSC_UNION_QUERY);\n    if (false) {}\n    return urlWithoutFlightParameters;\n}\nfunction doMpaNavigation(url) {\n    return {\n        flightData: urlToUrlWithoutFlightMarker(url).toString(),\n        canonicalUrl: undefined,\n        couldBeIntercepted: false,\n        prerendered: false,\n        postponed: false,\n        staleTime: -1\n    };\n}\nlet abortController = new AbortController();\nif (true) {\n    // Abort any in-flight requests when the page is unloaded, e.g. due to\n    // reloading the page or performing hard navigations. This allows us to ignore\n    // what would otherwise be a thrown TypeError when the browser cancels the\n    // requests.\n    window.addEventListener('pagehide', ()=>{\n        abortController.abort();\n    });\n    // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n    // and the JavaScript execution context is restored by the browser.\n    window.addEventListener('pageshow', ()=>{\n        abortController = new AbortController();\n    });\n}\nasync function fetchServerResponse(url, options) {\n    const { flightRouterState, nextUrl, prefetchKind } = options;\n    const headers = {\n        // Enable flight response\n        [_approuterheaders.RSC_HEADER]: '1',\n        // Provide the current router state\n        [_approuterheaders.NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(JSON.stringify(flightRouterState))\n    };\n    /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */ if (prefetchKind === _routerreducertypes.PrefetchKind.AUTO) {\n        headers[_approuterheaders.NEXT_ROUTER_PREFETCH_HEADER] = '1';\n    }\n    if ( true && options.isHmrRefresh) {\n        headers[_approuterheaders.NEXT_HMR_REFRESH_HEADER] = '1';\n    }\n    if (nextUrl) {\n        headers[_approuterheaders.NEXT_URL] = nextUrl;\n    }\n    try {\n        var _res_headers_get;\n        // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n        // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n        // Otherwise, all other prefetches are sent with a \"low\" priority.\n        // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n        const fetchPriority = prefetchKind ? prefetchKind === _routerreducertypes.PrefetchKind.TEMPORARY ? 'high' : 'low' : 'auto';\n        if (false) {}\n        const res = await createFetch(url, headers, fetchPriority, abortController.signal);\n        const responseUrl = urlToUrlWithoutFlightMarker(res.url);\n        const canonicalUrl = res.redirected ? responseUrl : undefined;\n        const contentType = res.headers.get('content-type') || '';\n        const interception = !!((_res_headers_get = res.headers.get('vary')) == null ? void 0 : _res_headers_get.includes(_approuterheaders.NEXT_URL));\n        const postponed = !!res.headers.get(_approuterheaders.NEXT_DID_POSTPONE_HEADER);\n        const staleTimeHeaderSeconds = res.headers.get(_approuterheaders.NEXT_ROUTER_STALE_TIME_HEADER);\n        const staleTime = staleTimeHeaderSeconds !== null ? parseInt(staleTimeHeaderSeconds, 10) * 1000 : -1;\n        let isFlightResponse = contentType.startsWith(_approuterheaders.RSC_CONTENT_TYPE_HEADER);\n        if (false) {}\n        // If fetch returns something different than flight response handle it like a mpa navigation\n        // If the fetch was not 200, we also handle it like a mpa navigation\n        if (!isFlightResponse || !res.ok || !res.body) {\n            // in case the original URL came with a hash, preserve it before redirecting to the new URL\n            if (url.hash) {\n                responseUrl.hash = url.hash;\n            }\n            return doMpaNavigation(responseUrl.toString());\n        }\n        // We may navigate to a page that requires a different Webpack runtime.\n        // In prod, every page will have the same Webpack runtime.\n        // In dev, the Webpack runtime is minimal for each page.\n        // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n        if (true) {\n            await (__webpack_require__(/*! ../react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\").waitForWebpackRuntimeHotUpdate)();\n        }\n        // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n        const flightStream = postponed ? createUnclosingPrefetchStream(res.body) : res.body;\n        const response = await createFromNextReadableStream(flightStream);\n        if ((0, _appbuildid.getAppBuildId)() !== response.b) {\n            return doMpaNavigation(res.url);\n        }\n        return {\n            flightData: (0, _flightdatahelpers.normalizeFlightData)(response.f),\n            canonicalUrl: canonicalUrl,\n            couldBeIntercepted: interception,\n            prerendered: response.S,\n            postponed,\n            staleTime\n        };\n    } catch (err) {\n        if (!abortController.signal.aborted) {\n            console.error(\"Failed to fetch RSC payload for \" + url + \". Falling back to browser navigation.\", err);\n        }\n        // If fetch fails handle it like a mpa navigation\n        // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n        // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n        return {\n            flightData: url.toString(),\n            canonicalUrl: undefined,\n            couldBeIntercepted: false,\n            prerendered: false,\n            postponed: false,\n            staleTime: -1\n        };\n    }\n}\nfunction createFetch(url, headers, fetchPriority, signal) {\n    const fetchUrl = new URL(url);\n    // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n    // cache busting search param) from the request so they're\n    // maximally cacheable.\n    (0, _setcachebustingsearchparam.setCacheBustingSearchParam)(fetchUrl, headers);\n    if (false) {}\n    if (false) {}\n    return fetch(fetchUrl, {\n        // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n        credentials: 'same-origin',\n        headers,\n        priority: fetchPriority || undefined,\n        signal\n    });\n}\nfunction createFromNextReadableStream(flightStream) {\n    return createFromReadableStream(flightStream, {\n        callServer: _appcallserver.callServer,\n        findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n    });\n}\nfunction createUnclosingPrefetchStream(originalFlightStream) {\n    // When PPR is enabled, prefetch streams may contain references that never\n    // resolve, because that's how we encode dynamic data access. In the decoded\n    // object returned by the Flight client, these are reified into hanging\n    // promises that suspend during render, which is effectively what we want.\n    // The UI resolves when it switches to the dynamic data stream\n    // (via useDeferredValue(dynamic, static)).\n    //\n    // However, the Flight implementation currently errors if the server closes\n    // the response before all the references are resolved. As a cheat to work\n    // around this, we wrap the original stream in a new stream that never closes,\n    // and therefore doesn't error.\n    const reader = originalFlightStream.getReader();\n    return new ReadableStream({\n        async pull (controller) {\n            while(true){\n                const { done, value } = await reader.read();\n                if (!done) {\n                    // Pass to the target stream and keep consuming the Flight response\n                    // from the server.\n                    controller.enqueue(value);\n                    continue;\n                }\n                // The server stream has closed. Exit, but intentionally do not close\n                // the target stream.\n                return;\n            }\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=fetch-server-response.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DYNAMIC_STALETIME_MS: function() {\n        return DYNAMIC_STALETIME_MS;\n    },\n    STATIC_STALETIME_MS: function() {\n        return STATIC_STALETIME_MS;\n    },\n    createSeededPrefetchCacheEntry: function() {\n        return createSeededPrefetchCacheEntry;\n    },\n    getOrCreatePrefetchCacheEntry: function() {\n        return getOrCreatePrefetchCacheEntry;\n    },\n    prunePrefetchCache: function() {\n        return prunePrefetchCache;\n    }\n});\nconst _fetchserverresponse = __webpack_require__(/*! ./fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst INTERCEPTION_CACHE_KEY_MARKER = '%';\n/**\n * Creates a cache key for the router prefetch cache\n *\n * @param url - The URL being navigated to\n * @param nextUrl - an internal URL, primarily used for handling rewrites. Defaults to '/'.\n * @return The generated prefetch cache key.\n */ function createPrefetchCacheKeyImpl(url, includeSearchParams, prefix) {\n    // Initially we only use the pathname as the cache key. We don't want to include\n    // search params so that multiple URLs with the same search parameter can re-use\n    // loading states.\n    let pathnameFromUrl = url.pathname;\n    // RSC responses can differ based on search params, specifically in the case where we aren't\n    // returning a partial response (ie with `PrefetchKind.AUTO`).\n    // In the auto case, since loading.js & layout.js won't have access to search params,\n    // we can safely re-use that cache entry. But for full prefetches, we should not\n    // re-use the cache entry as the response may differ.\n    if (includeSearchParams) {\n        // if we have a full prefetch, we can include the search param in the key,\n        // as we'll be getting back a full response. The server might have read the search\n        // params when generating the full response.\n        pathnameFromUrl += url.search;\n    }\n    if (prefix) {\n        return \"\" + prefix + INTERCEPTION_CACHE_KEY_MARKER + pathnameFromUrl;\n    }\n    return pathnameFromUrl;\n}\nfunction createPrefetchCacheKey(url, kind, nextUrl) {\n    return createPrefetchCacheKeyImpl(url, kind === _routerreducertypes.PrefetchKind.FULL, nextUrl);\n}\nfunction getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing) {\n    if (kind === void 0) kind = _routerreducertypes.PrefetchKind.TEMPORARY;\n    // We first check if there's a more specific interception route prefetch entry\n    // This is because when we detect a prefetch that corresponds with an interception route, we prefix it with nextUrl (see `createPrefetchCacheKey`)\n    // to avoid conflicts with other pages that may have the same URL but render different things depending on the `Next-URL` header.\n    for (const maybeNextUrl of [\n        nextUrl,\n        null\n    ]){\n        const cacheKeyWithParams = createPrefetchCacheKeyImpl(url, true, maybeNextUrl);\n        const cacheKeyWithoutParams = createPrefetchCacheKeyImpl(url, false, maybeNextUrl);\n        // First, we check if we have a cache entry that exactly matches the URL\n        const cacheKeyToUse = url.search ? cacheKeyWithParams : cacheKeyWithoutParams;\n        const existingEntry = prefetchCache.get(cacheKeyToUse);\n        if (existingEntry && allowAliasing) {\n            // We know we're returning an aliased entry when the pathname matches but the search params don't,\n            const isAliased = existingEntry.url.pathname === url.pathname && existingEntry.url.search !== url.search;\n            if (isAliased) {\n                return {\n                    ...existingEntry,\n                    aliased: true\n                };\n            }\n            return existingEntry;\n        }\n        // If the request contains search params, and we're not doing a full prefetch, we can return the\n        // param-less entry if it exists.\n        // This is technically covered by the check at the bottom of this function, which iterates over cache entries,\n        // but lets us arrive there quicker in the param-full case.\n        const entryWithoutParams = prefetchCache.get(cacheKeyWithoutParams);\n        if (false) {}\n    }\n    // If we've gotten to this point, we didn't find a specific cache entry that matched\n    // the request URL.\n    // We attempt a partial match by checking if there's a cache entry with the same pathname.\n    // Regardless of what we find, since it doesn't correspond with the requested URL, we'll mark it \"aliased\".\n    // This will signal to the router that it should only apply the loading state on the prefetched data.\n    if (false) {}\n    return undefined;\n}\nfunction getOrCreatePrefetchCacheEntry(param) {\n    let { url, nextUrl, tree, prefetchCache, kind, allowAliasing = true } = param;\n    const existingCacheEntry = getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing);\n    if (existingCacheEntry) {\n        // Grab the latest status of the cache entry and update it\n        existingCacheEntry.status = getPrefetchEntryCacheStatus(existingCacheEntry);\n        // when `kind` is provided, an explicit prefetch was requested.\n        // if the requested prefetch is \"full\" and the current cache entry wasn't, we want to re-prefetch with the new intent\n        const switchedToFullPrefetch = existingCacheEntry.kind !== _routerreducertypes.PrefetchKind.FULL && kind === _routerreducertypes.PrefetchKind.FULL;\n        if (switchedToFullPrefetch) {\n            // If we switched to a full prefetch, validate that the existing cache entry contained partial data.\n            // It's possible that the cache entry was seeded with full data but has a cache type of \"auto\" (ie when cache entries\n            // are seeded but without a prefetch intent)\n            existingCacheEntry.data.then((prefetchResponse)=>{\n                const isFullPrefetch = Array.isArray(prefetchResponse.flightData) && prefetchResponse.flightData.some((flightData)=>{\n                    // If we started rendering from the root and we returned RSC data (seedData), we already had a full prefetch.\n                    return flightData.isRootRender && flightData.seedData !== null;\n                });\n                if (!isFullPrefetch) {\n                    return createLazyPrefetchEntry({\n                        tree,\n                        url,\n                        nextUrl,\n                        prefetchCache,\n                        // If we didn't get an explicit prefetch kind, we want to set a temporary kind\n                        // rather than assuming the same intent as the previous entry, to be consistent with how we\n                        // lazily create prefetch entries when intent is left unspecified.\n                        kind: kind != null ? kind : _routerreducertypes.PrefetchKind.TEMPORARY\n                    });\n                }\n            });\n        }\n        // If the existing cache entry was marked as temporary, it means it was lazily created when attempting to get an entry,\n        // where we didn't have the prefetch intent. Now that we have the intent (in `kind`), we want to update the entry to the more accurate kind.\n        if (kind && existingCacheEntry.kind === _routerreducertypes.PrefetchKind.TEMPORARY) {\n            existingCacheEntry.kind = kind;\n        }\n        // We've determined that the existing entry we found is still valid, so we return it.\n        return existingCacheEntry;\n    }\n    // If we didn't return an entry, create a new one.\n    return createLazyPrefetchEntry({\n        tree,\n        url,\n        nextUrl,\n        prefetchCache,\n        kind: kind || _routerreducertypes.PrefetchKind.TEMPORARY\n    });\n}\n/*\n * Used to take an existing cache entry and prefix it with the nextUrl, if it exists.\n * This ensures that we don't have conflicting cache entries for the same URL (as is the case with route interception).\n */ function prefixExistingPrefetchCacheEntry(param) {\n    let { url, nextUrl, prefetchCache, existingCacheKey } = param;\n    const existingCacheEntry = prefetchCache.get(existingCacheKey);\n    if (!existingCacheEntry) {\n        // no-op -- there wasn't an entry to move\n        return;\n    }\n    const newCacheKey = createPrefetchCacheKey(url, existingCacheEntry.kind, nextUrl);\n    prefetchCache.set(newCacheKey, {\n        ...existingCacheEntry,\n        key: newCacheKey\n    });\n    prefetchCache.delete(existingCacheKey);\n    return newCacheKey;\n}\nfunction createSeededPrefetchCacheEntry(param) {\n    let { nextUrl, tree, prefetchCache, url, data, kind } = param;\n    // The initial cache entry technically includes full data, but it isn't explicitly prefetched -- we just seed the\n    // prefetch cache so that we can skip an extra prefetch request later, since we already have the data.\n    // if the prefetch corresponds with an interception route, we use the nextUrl to prefix the cache key\n    const prefetchCacheKey = data.couldBeIntercepted ? createPrefetchCacheKey(url, kind, nextUrl) : createPrefetchCacheKey(url, kind);\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data: Promise.resolve(data),\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: Date.now(),\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\n/**\n * Creates a prefetch entry entry and enqueues a fetch request to retrieve the data.\n */ function createLazyPrefetchEntry(param) {\n    let { url, kind, tree, nextUrl, prefetchCache } = param;\n    const prefetchCacheKey = createPrefetchCacheKey(url, kind);\n    // initiates the fetch request for the prefetch and attaches a listener\n    // to the promise to update the prefetch cache entry when the promise resolves (if necessary)\n    const data = _prefetchreducer.prefetchQueue.enqueue(()=>(0, _fetchserverresponse.fetchServerResponse)(url, {\n            flightRouterState: tree,\n            nextUrl,\n            prefetchKind: kind\n        }).then((prefetchResponse)=>{\n            // TODO: `fetchServerResponse` should be more tighly coupled to these prefetch cache operations\n            // to avoid drift between this cache key prefixing logic\n            // (which is currently directly influenced by the server response)\n            let newCacheKey;\n            if (prefetchResponse.couldBeIntercepted) {\n                // Determine if we need to prefix the cache key with the nextUrl\n                newCacheKey = prefixExistingPrefetchCacheEntry({\n                    url,\n                    existingCacheKey: prefetchCacheKey,\n                    nextUrl,\n                    prefetchCache\n                });\n            }\n            // If the prefetch was a cache hit, we want to update the existing cache entry to reflect that it was a full prefetch.\n            // This is because we know that a static response will contain the full RSC payload, and can be updated to respect the `static`\n            // staleTime.\n            if (prefetchResponse.prerendered) {\n                const existingCacheEntry = prefetchCache.get(newCacheKey != null ? newCacheKey : prefetchCacheKey);\n                if (existingCacheEntry) {\n                    existingCacheEntry.kind = _routerreducertypes.PrefetchKind.FULL;\n                    if (prefetchResponse.staleTime !== -1) {\n                        // This is the stale time that was collected by the server during\n                        // static generation. Use this in place of the default stale time.\n                        existingCacheEntry.staleTime = prefetchResponse.staleTime;\n                    }\n                }\n            }\n            return prefetchResponse;\n        }));\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data,\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: null,\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\nfunction prunePrefetchCache(prefetchCache) {\n    for (const [href, prefetchCacheEntry] of prefetchCache){\n        if (getPrefetchEntryCacheStatus(prefetchCacheEntry) === _routerreducertypes.PrefetchCacheEntryStatus.expired) {\n            prefetchCache.delete(href);\n        }\n    }\n}\nconst DYNAMIC_STALETIME_MS = Number(\"0\") * 1000;\nconst STATIC_STALETIME_MS = Number(\"300\") * 1000;\nfunction getPrefetchEntryCacheStatus(param) {\n    let { kind, prefetchTime, lastUsedTime, staleTime } = param;\n    if (staleTime !== -1) {\n        // `staleTime` is the value sent by the server during static generation.\n        // When this is available, it takes precedence over any of the heuristics\n        // that follow.\n        //\n        // TODO: When PPR is enabled, the server will *always* return a stale time\n        // when prefetching. We should never use a prefetch entry that hasn't yet\n        // received data from the server. So the only two cases should be 1) we use\n        // the server-generated stale time 2) the unresolved entry is discarded.\n        return Date.now() < prefetchTime + staleTime ? _routerreducertypes.PrefetchCacheEntryStatus.fresh : _routerreducertypes.PrefetchCacheEntryStatus.stale;\n    }\n    // We will re-use the cache entry data for up to the `dynamic` staletime window.\n    if (Date.now() < (lastUsedTime != null ? lastUsedTime : prefetchTime) + DYNAMIC_STALETIME_MS) {\n        return lastUsedTime ? _routerreducertypes.PrefetchCacheEntryStatus.reusable : _routerreducertypes.PrefetchCacheEntryStatus.fresh;\n    }\n    // For \"auto\" prefetching, we'll re-use only the loading boundary for up to `static` staletime window.\n    // A stale entry will only re-use the `loading` boundary, not the full data.\n    // This will trigger a \"lazy fetch\" for the full data.\n    if (kind === _routerreducertypes.PrefetchKind.AUTO) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.stale;\n        }\n    }\n    // for \"full\" prefetching, we'll re-use the cache entry data for up to `static` staletime window.\n    if (kind === _routerreducertypes.PrefetchKind.FULL) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.reusable;\n        }\n    }\n    return _routerreducertypes.PrefetchCacheEntryStatus.expired;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=prefetch-cache-utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js ***!
  \*************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hmrRefreshReducer\", ({\n    enumerable: true,\n    get: function() {\n        return hmrRefreshReducer;\n    }\n}));\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(state, action) {\n    const { origin } = action;\n    const mutable = {};\n    const href = state.canonicalUrl;\n    mutable.preserveCustomHistoryState = false;\n    const cache = (0, _approuter.createEmptyCacheNode)();\n    // If the current tree was intercepted, the nextUrl should be included in the request.\n    // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n    const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree);\n    // TODO-APP: verify that `href` is not an external url.\n    // Fetch data from the root of the tree.\n    const navigatedAt = Date.now();\n    cache.lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(href, origin), {\n        flightRouterState: [\n            state.tree[0],\n            state.tree[1],\n            state.tree[2],\n            'refetch'\n        ],\n        nextUrl: includeNextUrl ? state.nextUrl : null,\n        isHmrRefresh: true\n    });\n    return cache.lazyData.then((param)=>{\n        let { flightData, canonicalUrl: canonicalUrlOverride } = param;\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === 'string') {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        // Remove cache.lazyData as it has been resolved at this point.\n        cache.lazyData = null;\n        let currentTree = state.tree;\n        let currentCache = state.cache;\n        for (const normalizedFlightData of flightData){\n            const { tree: treePatch, isRootRender } = normalizedFlightData;\n            if (!isRootRender) {\n                // TODO-APP: handle this case better\n                console.log('REFRESH FAILED');\n                return state;\n            }\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                ''\n            ], currentTree, treePatch, state.canonicalUrl);\n            if (newTree === null) {\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, href, state.pushRef.pendingPush);\n            }\n            const canonicalUrlOverrideHref = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : undefined;\n            if (canonicalUrlOverride) {\n                mutable.canonicalUrl = canonicalUrlOverrideHref;\n            }\n            const applied = (0, _applyflightdata.applyFlightData)(navigatedAt, currentCache, cache, normalizedFlightData);\n            if (applied) {\n                mutable.cache = cache;\n                currentCache = cache;\n            }\n            mutable.patchedTree = newTree;\n            mutable.canonicalUrl = href;\n            currentTree = newTree;\n        }\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nfunction hmrRefreshReducerNoop(state, _action) {\n    return state;\n}\nconst hmrRefreshReducer =  false ? 0 : hmrRefreshReducerImpl;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hmr-refresh-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/use-action-queue.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    dispatchAppRouterAction: function() {\n        return dispatchAppRouterAction;\n    },\n    useActionQueue: function() {\n        return useActionQueue;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _isthenable = __webpack_require__(/*! ../../shared/lib/is-thenable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/is-thenable.js\");\n// The app router state lives outside of React, so we can import the dispatch\n// method directly wherever we need it, rather than passing it around via props\n// or context.\nlet dispatch = null;\nfunction dispatchAppRouterAction(action) {\n    if (dispatch === null) {\n        throw Object.defineProperty(new Error('Internal Next.js error: Router action dispatched before initialization.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E668\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    dispatch(action);\n}\nfunction useActionQueue(actionQueue) {\n    _s();\n    const [state, setState] = _react.default.useState(actionQueue.state);\n    // Because of a known issue that requires to decode Flight streams inside the\n    // render phase, we have to be a bit clever and assign the dispatch method to\n    // a module-level variable upon initialization. The useState hook in this\n    // module only exists to synchronize state that lives outside of React.\n    // Ideally, what we'd do instead is pass the state as a prop to root.render;\n    // this is conceptually how we're modeling the app router state, despite the\n    // weird implementation details.\n    if (true) {\n        const useSyncDevRenderIndicator = (__webpack_require__(/*! ./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.js\").useSyncDevRenderIndicator);\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const syncDevRenderIndicator = useSyncDevRenderIndicator();\n        dispatch = (action)=>{\n            syncDevRenderIndicator(()=>{\n                actionQueue.dispatch(action, setState);\n            });\n        };\n    } else {}\n    return (0, _isthenable.isThenable)(state) ? (0, _react.use)(state) : state;\n}\n_s(useActionQueue, \"Rp0Tj1zyE8LTecjN/cjTzn46xPo=\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-action-queue.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js ***!
  \**************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This module can be shared between both pages router and app router\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"onRecoverableError\", ({\n    enumerable: true,\n    get: function() {\n        return onRecoverableError;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _reportglobalerror = __webpack_require__(/*! ./report-global-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/report-global-error.js\");\nconst _stitchederror = __webpack_require__(/*! ../components/errors/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst onRecoverableError = (error, errorInfo)=>{\n    // x-ref: https://github.com/facebook/react/pull/28736\n    const cause = (0, _iserror.default)(error) && 'cause' in error ? error.cause : error;\n    const stitchedError = (0, _stitchederror.getReactStitchedError)(cause);\n    // In development mode, pass along the component stack to the error\n    if ( true && errorInfo.componentStack) {\n        ;\n        stitchedError._componentStack = errorInfo.componentStack;\n    }\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(cause)) return;\n    (0, _reportglobalerror.reportGlobalError)(stitchedError);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=on-recoverable-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PathParamsContext: function() {\n        return PathParamsContext;\n    },\n    PathnameContext: function() {\n        return PathnameContext;\n    },\n    SearchParamsContext: function() {\n        return SearchParamsContext;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst SearchParamsContext = (0, _react.createContext)(null);\nconst PathnameContext = (0, _react.createContext)(null);\nconst PathParamsContext = (0, _react.createContext)(null);\nif (true) {\n    SearchParamsContext.displayName = 'SearchParamsContext';\n    PathnameContext.displayName = 'PathnameContext';\n    PathParamsContext.displayName = 'PathParamsContext';\n} //# sourceMappingURL=hooks-client-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9ob29rcy1jbGllbnQtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFPYUEsaUJBQWlCO2VBQWpCQTs7SUFEQUMsZUFBZTtlQUFmQTs7SUFEQUMsbUJBQW1CO2VBQW5CQTs7O21DQUhpQjtBQUd2QixNQUFNQSxzQkFBc0JDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQXNDO0FBQ2xFLE1BQU1GLGtCQUFrQkUsQ0FBQUEsR0FBQUEsT0FBQUEsYUFBQUEsRUFBNkI7QUFDckQsTUFBTUgsb0JBQW9CRyxDQUFBQSxHQUFBQSxPQUFBQSxhQUFBQSxFQUE2QjtBQUU5RCxJQUFJQyxJQUFvQixFQUFtQjtJQUN6Q0Ysb0JBQW9CSyxXQUFXLEdBQUc7SUFDbENOLGdCQUFnQk0sV0FBVyxHQUFHO0lBQzlCUCxrQkFBa0JPLFdBQVcsR0FBRztBQUNsQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVbWVyIEZhcm9vcVxcRGVza3RvcFxcc3JjXFxzaGFyZWRcXGxpYlxcaG9va3MtY2xpZW50LWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB0eXBlIHsgUGFyYW1zIH0gZnJvbSAnLi4vLi4vc2VydmVyL3JlcXVlc3QvcGFyYW1zJ1xuXG5leHBvcnQgY29uc3QgU2VhcmNoUGFyYW1zQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8VVJMU2VhcmNoUGFyYW1zIHwgbnVsbD4obnVsbClcbmV4cG9ydCBjb25zdCBQYXRobmFtZUNvbnRleHQgPSBjcmVhdGVDb250ZXh0PHN0cmluZyB8IG51bGw+KG51bGwpXG5leHBvcnQgY29uc3QgUGF0aFBhcmFtc0NvbnRleHQgPSBjcmVhdGVDb250ZXh0PFBhcmFtcyB8IG51bGw+KG51bGwpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIFNlYXJjaFBhcmFtc0NvbnRleHQuZGlzcGxheU5hbWUgPSAnU2VhcmNoUGFyYW1zQ29udGV4dCdcbiAgUGF0aG5hbWVDb250ZXh0LmRpc3BsYXlOYW1lID0gJ1BhdGhuYW1lQ29udGV4dCdcbiAgUGF0aFBhcmFtc0NvbnRleHQuZGlzcGxheU5hbWUgPSAnUGF0aFBhcmFtc0NvbnRleHQnXG59XG4iXSwibmFtZXMiOlsiUGF0aFBhcmFtc0NvbnRleHQiLCJQYXRobmFtZUNvbnRleHQiLCJTZWFyY2hQYXJhbXNDb250ZXh0IiwiY3JlYXRlQ29udGV4dCIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\n"));

/***/ })

});