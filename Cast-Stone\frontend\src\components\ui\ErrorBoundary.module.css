.errorBoundary {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: var(--background-light);
}

.errorContainer {
  max-width: 600px;
  width: 100%;
  text-align: center;
  background: var(--cart-bg);
  padding: 48px 32px;
  border-radius: 12px;
  box-shadow: 0 4px 20px var(--cart-shadow);
}

.errorIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
  background: var(--cart-error);
  border-radius: 50%;
  color: white;
}

.errorIcon svg {
  width: 40px;
  height: 40px;
}

.errorTitle {
  font-size: 2rem;
  font-weight: 700;
  color: var(--cart-text-primary);
  margin: 0 0 16px 0;
}

.errorMessage {
  font-size: 1.1rem;
  color: var(--cart-text-secondary);
  line-height: 1.6;
  margin: 0 0 32px 0;
}

.errorActions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.retryButton,
.homeButton {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 16px;
}

.retryButton {
  background: var(--primary-color);
  color: white;
}

.retryButton:hover {
  background: var(--cart-primary-hover);
  transform: translateY(-1px);
}

.homeButton {
  background: transparent;
  color: var(--cart-text-secondary);
  border: 2px solid var(--cart-border);
}

.homeButton:hover {
  background: var(--cart-border);
  color: var(--cart-text-primary);
}

.retryButton svg,
.homeButton svg {
  width: 18px;
  height: 18px;
}

.errorDetails {
  margin-top: 32px;
  text-align: left;
  background: var(--background-light);
  border-radius: 8px;
  padding: 16px;
}

.errorDetails summary {
  cursor: pointer;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin-bottom: 12px;
}

.errorDetails summary:hover {
  color: var(--primary-color);
}

.errorStack {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.errorStack h4 {
  color: var(--cart-text-primary);
  margin: 16px 0 8px 0;
  font-size: 14px;
}

.errorStack h4:first-child {
  margin-top: 0;
}

.errorStack pre {
  background: var(--cart-bg);
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid var(--cart-error);
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: var(--cart-text-secondary);
  margin: 0;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .errorBoundary {
    padding: 16px;
  }
  
  .errorContainer {
    padding: 32px 24px;
  }
  
  .errorIcon {
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
  }
  
  .errorIcon svg {
    width: 30px;
    height: 30px;
  }
  
  .errorTitle {
    font-size: 1.5rem;
  }
  
  .errorMessage {
    font-size: 1rem;
  }
  
  .errorActions {
    flex-direction: column;
    align-items: center;
  }
  
  .retryButton,
  .homeButton {
    width: 100%;
    max-width: 200px;
    justify-content: center;
  }
  
  .errorDetails {
    padding: 12px;
  }
  
  .errorStack {
    font-size: 11px;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .errorContainer {
    border: 2px solid var(--cart-text-primary);
  }
  
  .retryButton {
    border: 2px solid var(--primary-color);
  }
  
  .homeButton {
    border-width: 2px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .retryButton:hover,
  .homeButton:hover {
    transform: none;
  }
}
