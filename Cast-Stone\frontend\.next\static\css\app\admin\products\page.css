/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/app/admin/products/page.module.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
.page_productsPage__c61xh {
  padding: 0;
}

.page_loading__gB3TQ {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 20px;
}

.page_spinner__uXT76 {
  width: 40px;
  height: 40px;
  border: 4px solid var(--cart-border);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: page_spin__9tRz9 1s linear infinite;
}

.page_loading__gB3TQ p {
  color: var(--cart-text-secondary);
  font-size: 1.1rem;
}

@keyframes page_spin__9tRz9 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.page_header__lx787 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.page_title__BTLt0 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--cart-text-primary);
  margin: 0 0 8px 0;
}

.page_subtitle__g7kLI {
  color: var(--cart-text-secondary);
  margin: 0;
  font-size: 1rem;
}

.page_headerActions__F00rA {
  display: flex;
  gap: 12px;
}

.page_primaryButton__LikUY {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page_primaryButton__LikUY:hover {
  background: var(--cart-primary-hover);
  transform: translateY(-1px);
}

.page_primaryButton__LikUY svg {
  width: 18px;
  height: 18px;
}

.page_secondaryButton__KN_To {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: white;
  color: var(--cart-text-secondary);
  border: 1px solid var(--cart-border);
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page_secondaryButton__KN_To:hover {
  background: var(--background-light);
  color: var(--cart-text-primary);
}

.page_secondaryButton__KN_To svg {
  width: 18px;
  height: 18px;
}

.page_toolbar__zbC_Q {
  background: white;
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.page_searchContainer__egPNk {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.page_searchIcon__8bDVQ {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  color: var(--cart-text-muted);
}

.page_searchInput__wqiCN {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid var(--cart-border);
  border-radius: 8px;
  font-size: 14px;
  background: var(--background-light);
  transition: all 0.2s ease;
}

.page_searchInput__wqiCN:focus {
  outline: none;
  border-color: var(--primary-color);
  background: white;
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.page_filters__8ys8Y {
  position: relative;
}

.page_filterButton__ljkjJ {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: white;
  border: 1px solid var(--cart-border);
  border-radius: 8px;
  color: var(--cart-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.page_filterButton__ljkjJ:hover,
.page_filterButton__ljkjJ.page_active__CJm9M {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.page_filterButton__ljkjJ svg {
  width: 16px;
  height: 16px;
}

.page_filterDropdown__Ev5PX {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid var(--cart-border);
  border-radius: 8px;
  box-shadow: 0 4px 20px var(--cart-shadow);
  padding: 20px;
  min-width: 250px;
  z-index: 100;
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.page_filterGroup__dcFsg {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page_filterGroup__dcFsg label {
  font-size: 14px;
  font-weight: 600;
  color: var(--cart-text-primary);
}

.page_filterGroup__dcFsg select {
  padding: 8px 12px;
  border: 1px solid var(--cart-border);
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.page_bulkActions__gCwUw {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: var(--background-light);
  border-radius: 8px;
  border: 1px solid var(--cart-border);
}

.page_selectedCount__TM5XX {
  font-size: 14px;
  font-weight: 600;
  color: var(--cart-text-primary);
}

.page_bulkDeleteButton__xlIWC {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: var(--cart-error);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page_bulkDeleteButton__xlIWC:hover {
  background: #b91c1c;
}

.page_bulkDeleteButton__xlIWC svg {
  width: 14px;
  height: 14px;
}

.page_tableContainer__F2faG {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.page_table__zBUFg {
  width: 100%;
  border-collapse: collapse;
}

.page_table__zBUFg th {
  background: var(--background-light);
  padding: 16px 20px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: var(--cart-text-secondary);
  text-transform: uppercase;
  border-bottom: 1px solid var(--cart-border);
}

.page_table__zBUFg td {
  padding: 16px 20px;
  border-bottom: 1px solid var(--cart-border);
  vertical-align: middle;
}

.page_table__zBUFg tr:last-child td {
  border-bottom: none;
}

.page_table__zBUFg tr:hover {
  background: var(--background-light);
}

.page_productCell__AFeZk {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page_productImage__Y5jMp {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  overflow: hidden;
  background: var(--background-light);
  flex-shrink: 0;
}

.page_productImage__Y5jMp img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.page_imagePlaceholder__E5U_1 {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--cart-text-muted);
}

.page_imagePlaceholder__E5U_1 svg {
  width: 20px;
  height: 20px;
}

.page_productInfo__7kMOl {
  flex: 1;
  min-width: 0;
}

.page_productName__PMec2 {
  font-size: 14px;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.page_productDescription__YrzLY {
  font-size: 12px;
  color: var(--cart-text-secondary);
  margin: 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.page_category__kpX_1 {
  padding: 4px 8px;
  background: var(--background-light);
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  color: var(--cart-text-secondary);
  text-transform: capitalize;
}

.page_price__V0JoF {
  font-weight: 600;
  color: var(--cart-text-primary);
}

.page_stock__I8mUj {
  font-weight: 600;
  color: var(--cart-text-primary);
}

.page_stock__I8mUj.page_outOfStock__p0SFZ {
  color: var(--cart-error);
}

.page_status__fLp5x {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.page_status__fLp5x.page_active__CJm9M {
  background: #dcfce7;
  color: #166534;
}

.page_status__fLp5x.page_inactive__XXhbF {
  background: #fee2e2;
  color: #991b1b;
}

.page_status__fLp5x.page_draft__pC9BV {
  background: #fef3c7;
  color: #92400e;
}

.page_date__FlSl2 {
  font-size: 12px;
  color: var(--cart-text-secondary);
}

.page_actions__Rpilg {
  display: flex;
  gap: 4px;
}

.page_actionButton__LF18d {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  color: var(--cart-text-muted);
  transition: all 0.2s ease;
}

.page_actionButton__LF18d:hover {
  background: var(--background-light);
  color: var(--cart-text-primary);
}

.page_actionButton__LF18d svg {
  width: 16px;
  height: 16px;
}

.page_emptyState__stDKT {
  padding: 60px 20px;
  text-align: center;
  color: var(--cart-text-muted);
}

.page_emptyIcon__V4SDZ {
  width: 64px;
  height: 64px;
  margin: 0 auto 20px;
  color: var(--cart-text-muted);
}

.page_emptyIcon__V4SDZ svg {
  width: 100%;
  height: 100%;
}

.page_emptyState__stDKT h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin: 0 0 8px 0;
}

.page_emptyState__stDKT p {
  margin: 0;
  color: var(--cart-text-secondary);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .page_header__lx787 {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .page_title__BTLt0 {
    font-size: 1.5rem;
  }
  
  .page_headerActions__F00rA {
    width: 100%;
    justify-content: flex-start;
  }
  
  .page_toolbar__zbC_Q {
    padding: 16px;
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .page_searchContainer__egPNk {
    min-width: auto;
  }
  
  .page_table__zBUFg {
    font-size: 12px;
  }
  
  .page_table__zBUFg th,
  .page_table__zBUFg td {
    padding: 12px 8px;
  }
  
  .page_productCell__AFeZk {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .page_productImage__Y5jMp {
    width: 40px;
    height: 40px;
  }
  
  .page_actions__Rpilg {
    flex-direction: column;
  }
}

