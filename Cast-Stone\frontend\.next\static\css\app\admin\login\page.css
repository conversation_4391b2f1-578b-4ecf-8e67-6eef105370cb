/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/app/admin/login/page.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
.page_container__WsbUw {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.page_loginCard__Sejl_ {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
  max-width: 450px;
}

.page_header__yi1oV {
  background: var(--primary-color);
  padding: 32px;
  text-align: center;
  color: white;
}

.page_logo__7tFCW {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.page_logoIcon__2QJaE {
  width: 40px;
  height: 40px;
}

.page_logoTitle__H_1L1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.page_logoSubtitle__lHe94 {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 4px 0 0 0;
}

.page_form__0Mq_U {
  padding: 40px 32px;
}

.page_title__bYS1H {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--cart-text-primary);
  margin: 0 0 8px 0;
  text-align: center;
}

.page_subtitle__wDdUT {
  color: var(--cart-text-secondary);
  text-align: center;
  margin: 0 0 32px 0;
}

.page_formGroup__2FaIq {
  margin-bottom: 24px;
}

.page_label__JHLTl {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: var(--cart-text-primary);
  margin-bottom: 8px;
}

.page_labelIcon__Pch3R {
  width: 16px;
  height: 16px;
  color: var(--primary-color);
}

.page_input__ETyBp {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid var(--cart-border);
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s ease;
  background: white;
  color: var(--cart-text-primary);
}

.page_input__ETyBp:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.page_input__ETyBp.page_error__s0Aev {
  border-color: var(--cart-error);
}

.page_input__ETyBp:disabled {
  background: var(--background-light);
  cursor: not-allowed;
}

.page_passwordWrapper__zNdm3 {
  position: relative;
}

.page_passwordToggle__0P3ba {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: var(--cart-text-muted);
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.page_passwordToggle__0P3ba:hover {
  color: var(--cart-text-primary);
}

.page_passwordToggle__0P3ba:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.page_passwordToggle__0P3ba svg {
  width: 18px;
  height: 18px;
}

.page_errorMessage__qHz1Z {
  color: var(--cart-error);
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.page_submitButton__w1XKg {
  width: 100%;
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 52px;
}

.page_submitButton__w1XKg:hover:not(:disabled) {
  background: var(--cart-primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
}

.page_submitButton__w1XKg:disabled {
  background: var(--cart-text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.page_spinner__5Xvxg {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: page_spin__gcTOo 1s linear infinite;
}

@keyframes page_spin__gcTOo {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.page_footer__ZrAMS {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--cart-border);
  text-align: center;
}

.page_footerText__qFg18 {
  font-size: 12px;
  color: var(--cart-text-muted);
  margin: 0;
  line-height: 1.4;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .page_container__WsbUw {
    padding: 16px;
  }
  
  .page_loginCard__Sejl_ {
    max-width: 100%;
  }
  
  .page_header__yi1oV {
    padding: 24px;
  }
  
  .page_logo__7tFCW {
    flex-direction: column;
    gap: 12px;
  }
  
  .page_logoIcon__2QJaE {
    width: 32px;
    height: 32px;
  }
  
  .page_logoTitle__H_1L1 {
    font-size: 1.25rem;
  }
  
  .page_form__0Mq_U {
    padding: 32px 24px;
  }
  
  .page_title__bYS1H {
    font-size: 1.5rem;
  }
  
  .page_formGroup__2FaIq {
    margin-bottom: 20px;
  }
  
  .page_input__ETyBp {
    padding: 12px 14px;
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .page_loginCard__Sejl_ {
    border: 2px solid var(--cart-text-primary);
  }
  
  .page_input__ETyBp {
    border-width: 2px;
  }
  
  .page_submitButton__w1XKg {
    border: 2px solid var(--primary-color);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .page_submitButton__w1XKg:hover {
    transform: none;
  }
  
  .page_spinner__5Xvxg {
    animation: none;
  }
}

