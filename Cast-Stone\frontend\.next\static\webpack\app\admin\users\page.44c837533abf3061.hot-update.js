"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/users/page",{

/***/ "(app-pages-browser)/./src/app/admin/users/page.tsx":
/*!**************************************!*\
  !*** ./src/app/admin/users/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UsersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,Mail,Search,ShoppingBag,Trash2,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,Mail,Search,ShoppingBag,Trash2,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,Mail,Search,ShoppingBag,Trash2,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,Mail,Search,ShoppingBag,Trash2,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,Mail,Search,ShoppingBag,Trash2,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,Mail,Search,ShoppingBag,Trash2,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,Mail,Search,ShoppingBag,Trash2,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,Mail,Search,ShoppingBag,Trash2,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,Mail,Search,ShoppingBag,Trash2,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,Mail,Search,ShoppingBag,Trash2,User!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AdminContext */ \"(app-pages-browser)/./src/contexts/AdminContext.tsx\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./page.module.css */ \"(app-pages-browser)/./src/app/admin/users/page.module.css\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_page_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction UsersPage() {\n    _s();\n    const { apiCall, hasPermission } = (0,_contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__.useAdmin)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showUserModal, setShowUserModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20,\n        total: 0,\n        totalPages: 0\n    });\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: '',\n        dateFrom: '',\n        dateTo: '',\n        sortBy: 'createdAt',\n        sortOrder: 'desc'\n    });\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UsersPage.useEffect\": ()=>{\n            fetchUsers();\n        }\n    }[\"UsersPage.useEffect\"], [\n        pagination.page,\n        filters\n    ]);\n    const fetchUsers = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const queryParams = new URLSearchParams({\n                page: pagination.page.toString(),\n                limit: pagination.limit.toString(),\n                ...filters.search && {\n                    search: filters.search\n                },\n                ...filters.dateFrom && {\n                    dateFrom: filters.dateFrom\n                },\n                ...filters.dateTo && {\n                    dateTo: filters.dateTo\n                },\n                sortBy: filters.sortBy,\n                sortOrder: filters.sortOrder\n            });\n            const response = await apiCall(\"/admin/users?\".concat(queryParams));\n            if (response.success) {\n                setUsers(response.data.users);\n                setPagination((prev)=>({\n                        ...prev,\n                        total: response.data.pagination.total,\n                        totalPages: response.data.pagination.totalPages\n                    }));\n            } else {\n                setError(response.message || 'Failed to fetch users');\n            }\n        } catch (err) {\n            setError('Failed to fetch users');\n            console.error('Fetch users error:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n        setPagination((prev)=>({\n                ...prev,\n                page: 1\n            }));\n    };\n    const clearFilters = ()=>{\n        setFilters({\n            search: '',\n            dateFrom: '',\n            dateTo: '',\n            sortBy: 'createdAt',\n            sortOrder: 'desc'\n        });\n        setPagination((prev)=>({\n                ...prev,\n                page: 1\n            }));\n    };\n    const viewUserDetails = async (userId)=>{\n        try {\n            const response = await apiCall(\"/admin/users/\".concat(userId));\n            if (response.success) {\n                setSelectedUser(response.data.user);\n                setShowUserModal(true);\n            }\n        } catch (err) {\n            console.error('Failed to fetch user details:', err);\n        }\n    };\n    const deleteUser = async (userId, userName)=>{\n        if (!hasPermission('users', 'delete')) {\n            alert('You do not have permission to delete users');\n            return;\n        }\n        if (!confirm('Are you sure you want to delete user \"'.concat(userName, '\"? This action cannot be undone.'))) {\n            return;\n        }\n        try {\n            const response = await apiCall(\"/admin/users/\".concat(userId), {\n                method: 'DELETE'\n            });\n            if (response.success) {\n                setUsers((prev)=>prev.filter((user)=>user._id !== userId));\n                alert('User deleted successfully');\n            } else {\n                alert(response.message || 'Failed to delete user');\n            }\n        } catch (err) {\n            console.error('Delete user error:', err);\n            alert('Failed to delete user');\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD'\n        }).format(amount);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        });\n    };\n    const getCustomerSegment = (totalSpent, totalOrders)=>{\n        if (totalSpent >= 1000) return {\n            label: 'VIP',\n            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().segmentVip)\n        };\n        if (totalSpent >= 500) return {\n            label: 'Premium',\n            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().segmentPremium)\n        };\n        if (totalOrders >= 5) return {\n            label: 'Regular',\n            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().segmentRegular)\n        };\n        if (totalOrders >= 1) return {\n            label: 'Customer',\n            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().segmentCustomer)\n        };\n        return {\n            label: 'New',\n            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().segmentNew)\n        };\n    };\n    const handleExport = async ()=>{\n        try {\n            // Create CSV content\n            const csvContent = [\n                [\n                    'Name',\n                    'Email',\n                    'Registration Date',\n                    'Total Orders',\n                    'Total Spent',\n                    'Average Order',\n                    'Segment',\n                    'Last Order'\n                ],\n                ...users.map((user)=>{\n                    var _user_orderStats_averageOrderValue;\n                    const segment = getCustomerSegment(user.orderStats.totalSpent, user.orderStats.totalOrders);\n                    return [\n                        user.name,\n                        user.email,\n                        formatDate(user.createdAt),\n                        user.orderStats.totalOrders.toString(),\n                        user.orderStats.totalSpent.toString(),\n                        ((_user_orderStats_averageOrderValue = user.orderStats.averageOrderValue) === null || _user_orderStats_averageOrderValue === void 0 ? void 0 : _user_orderStats_averageOrderValue.toString()) || '0',\n                        segment.label,\n                        user.orderStats.lastOrderDate ? formatDate(user.orderStats.lastOrderDate) : 'Never'\n                    ];\n                })\n            ].map((row)=>row.join(',')).join('\\n');\n            // Create and download file\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"users-export-\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            window.URL.revokeObjectURL(url);\n        } catch (error) {\n            console.error('Export failed:', error);\n        }\n    };\n    if (loading && users.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().container),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().loading),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().spinner)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Loading users...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerLeft),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().title),\n                                children: \"Users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().subtitle),\n                                children: \"Manage customer accounts and view analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerRight),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().exportButton),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                \"Export\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filtersSection),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchBar),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchIcon)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search users by name or email...\",\n                                value: filters.search,\n                                onChange: (e)=>handleFilterChange('search', e.target.value),\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchInput)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterToggle), \" \").concat(showFilters ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                        onClick: ()=>setShowFilters(!showFilters),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            \"Filters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filtersPanel),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterRow),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"date\",\n                            value: filters.dateFrom,\n                            onChange: (e)=>handleFilterChange('dateFrom', e.target.value),\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterInput),\n                            placeholder: \"From Date\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"date\",\n                            value: filters.dateTo,\n                            onChange: (e)=>handleFilterChange('dateTo', e.target.value),\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterInput),\n                            placeholder: \"To Date\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: filters.sortBy,\n                            onChange: (e)=>handleFilterChange('sortBy', e.target.value),\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterSelect),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"createdAt\",\n                                    children: \"Registration Date\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"name\",\n                                    children: \"Name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"email\",\n                                    children: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"lastLogin\",\n                                    children: \"Last Login\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: filters.sortOrder,\n                            onChange: (e)=>handleFilterChange('sortOrder', e.target.value),\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterSelect),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"desc\",\n                                    children: \"Descending\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"asc\",\n                                    children: \"Ascending\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: clearFilters,\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().clearFilters),\n                            children: \"Clear\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().error),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 317,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().tableContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().table),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Customer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Registration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Orders\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Total Spent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Segment\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Last Order\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                children: users.map((user)=>{\n                                    const segment = getCustomerSegment(user.orderStats.totalSpent, user.orderStats.totalOrders);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().tableRow),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().userInfo),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().userAvatar),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                size: 20\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().userDetails),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().userName),\n                                                                    children: user.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().userEmail),\n                                                                    children: user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().date),\n                                                    children: formatDate(user.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().orderStats),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().orderCount),\n                                                            children: user.orderStats.totalOrders\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().avgOrder),\n                                                            children: [\n                                                                \"Avg: \",\n                                                                formatCurrency(user.orderStats.averageOrderValue || 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().totalSpent),\n                                                    children: formatCurrency(user.orderStats.totalSpent)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().segment), \" \").concat(segment.className),\n                                                    children: segment.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().date),\n                                                    children: user.orderStats.lastOrderDate ? formatDate(user.orderStats.lastOrderDate) : 'Never'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actions),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>viewUserDetails(user._id),\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"View Details\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        hasPermission('users', 'delete') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deleteUser(user._id, user.name),\n                                                            className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton), \" \").concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().deleteButton)),\n                                                            title: \"Delete User\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, user._id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    users.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().emptyState),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 48\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"No users found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No users match your current filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this),\n            pagination.totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().pagination),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setPagination((prev)=>({\n                                    ...prev,\n                                    page: prev.page - 1\n                                })),\n                        disabled: pagination.page === 1,\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().paginationButton),\n                        children: \"Previous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().paginationInfo),\n                        children: [\n                            \"Page \",\n                            pagination.page,\n                            \" of \",\n                            pagination.totalPages\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setPagination((prev)=>({\n                                    ...prev,\n                                    page: prev.page + 1\n                                })),\n                        disabled: pagination.page === pagination.totalPages,\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().paginationButton),\n                        children: \"Next\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 420,\n                columnNumber: 9\n            }, this),\n            showUserModal && selectedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modal),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalHeader),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    children: selectedUser.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowUserModal(false),\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().closeButton),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalBody),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().userDetailsGrid),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().userSection),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"Contact Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().infoItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: selectedUser.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().infoItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Joined \",\n                                                            formatDate(selectedUser.createdAt)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().userSection),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"Order Statistics\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statsGrid),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statItem),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                size: 20\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statValue),\n                                                                        children: selectedUser.orderStats.totalOrders\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statLabel),\n                                                                        children: \"Total Orders\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statItem),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                size: 20\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statValue),\n                                                                        children: formatCurrency(selectedUser.orderStats.totalSpent)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 483,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statLabel),\n                                                                        children: \"Total Spent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 486,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statItem),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_Mail_Search_ShoppingBag_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                size: 20\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statValue),\n                                                                        children: formatCurrency(selectedUser.orderStats.averageOrderValue || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().statLabel),\n                                                                        children: \"Average Order\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 495,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 445,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n        lineNumber: 232,\n        columnNumber: 5\n    }, this);\n}\n_s(UsersPage, \"XlIT6eFExTHMMiG3+IrlNMCDsp8=\", false, function() {\n    return [\n        _contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__.useAdmin\n    ];\n});\n_c = UsersPage;\nvar _c;\n$RefreshReg$(_c, \"UsersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/users/page.tsx\n"));

/***/ })

});